"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/react-table/build/lib/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@tanstack/react-table/build/lib/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColumnFaceting: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnFaceting),\n/* harmony export */   ColumnFiltering: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnFiltering),\n/* harmony export */   ColumnGrouping: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnGrouping),\n/* harmony export */   ColumnOrdering: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnOrdering),\n/* harmony export */   ColumnPinning: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnPinning),\n/* harmony export */   ColumnSizing: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnSizing),\n/* harmony export */   ColumnVisibility: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnVisibility),\n/* harmony export */   GlobalFaceting: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.GlobalFaceting),\n/* harmony export */   GlobalFiltering: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.GlobalFiltering),\n/* harmony export */   Headers: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.Headers),\n/* harmony export */   RowExpanding: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowExpanding),\n/* harmony export */   RowPagination: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowPagination),\n/* harmony export */   RowPinning: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowPinning),\n/* harmony export */   RowSelection: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowSelection),\n/* harmony export */   RowSorting: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowSorting),\n/* harmony export */   _getVisibleLeafColumns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__._getVisibleLeafColumns),\n/* harmony export */   aggregationFns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.aggregationFns),\n/* harmony export */   buildHeaderGroups: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.buildHeaderGroups),\n/* harmony export */   createCell: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createCell),\n/* harmony export */   createColumn: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createColumn),\n/* harmony export */   createColumnHelper: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createColumnHelper),\n/* harmony export */   createRow: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createRow),\n/* harmony export */   createTable: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createTable),\n/* harmony export */   defaultColumnSizing: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.defaultColumnSizing),\n/* harmony export */   expandRows: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.expandRows),\n/* harmony export */   filterFns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.filterFns),\n/* harmony export */   flattenBy: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.flattenBy),\n/* harmony export */   flexRender: () => (/* binding */ flexRender),\n/* harmony export */   functionalUpdate: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.functionalUpdate),\n/* harmony export */   getCoreRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getCoreRowModel),\n/* harmony export */   getExpandedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getExpandedRowModel),\n/* harmony export */   getFacetedMinMaxValues: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFacetedMinMaxValues),\n/* harmony export */   getFacetedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFacetedRowModel),\n/* harmony export */   getFacetedUniqueValues: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFacetedUniqueValues),\n/* harmony export */   getFilteredRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFilteredRowModel),\n/* harmony export */   getGroupedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getGroupedRowModel),\n/* harmony export */   getMemoOptions: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getMemoOptions),\n/* harmony export */   getPaginationRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getPaginationRowModel),\n/* harmony export */   getSortedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getSortedRowModel),\n/* harmony export */   isFunction: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isFunction),\n/* harmony export */   isNumberArray: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isNumberArray),\n/* harmony export */   isRowSelected: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isRowSelected),\n/* harmony export */   isSubRowSelected: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isSubRowSelected),\n/* harmony export */   makeStateUpdater: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.makeStateUpdater),\n/* harmony export */   memo: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.memo),\n/* harmony export */   noop: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.noop),\n/* harmony export */   orderColumns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.orderColumns),\n/* harmony export */   passiveEventSupported: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.passiveEventSupported),\n/* harmony export */   reSplitAlphaNumeric: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.reSplitAlphaNumeric),\n/* harmony export */   selectRowsFn: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.selectRowsFn),\n/* harmony export */   shouldAutoRemoveFilter: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.shouldAutoRemoveFilter),\n/* harmony export */   sortingFns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.sortingFns),\n/* harmony export */   useReactTable: () => (/* binding */ useReactTable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/table-core */ \"(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs\");\n/**\n   * react-table\n   *\n   * Copyright (c) TanStack\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE.md file in the root directory of this source tree.\n   *\n   * @license MIT\n   */\n\n\n\n\n//\n\n/**\n * If rendering headers, cells, or footers with custom markup, use flexRender instead of `cell.getValue()` or `cell.renderValue()`.\n */\nfunction flexRender(Comp, props) {\n  return !Comp ? null : isReactComponent(Comp) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Comp, props) : Comp;\n}\nfunction isReactComponent(component) {\n  return isClassComponent(component) || typeof component === 'function' || isExoticComponent(component);\n}\nfunction isClassComponent(component) {\n  return typeof component === 'function' && (() => {\n    const proto = Object.getPrototypeOf(component);\n    return proto.prototype && proto.prototype.isReactComponent;\n  })();\n}\nfunction isExoticComponent(component) {\n  return typeof component === 'object' && typeof component.$$typeof === 'symbol' && ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description);\n}\nfunction useReactTable(options) {\n  // Compose in the generic options to the user options\n  const resolvedOptions = {\n    state: {},\n    // Dummy state\n    onStateChange: () => {},\n    // noop\n    renderFallbackValue: null,\n    ...options\n  };\n\n  // Create a new table and store it in state\n  const [tableRef] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => ({\n    current: (0,_tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createTable)(resolvedOptions)\n  }));\n\n  // By default, manage table state here using the table's initial state\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => tableRef.current.initialState);\n\n  // Compose the default state above with any user state. This will allow the user\n  // to only control a subset of the state if desired.\n  tableRef.current.setOptions(prev => ({\n    ...prev,\n    ...options,\n    state: {\n      ...state,\n      ...options.state\n    },\n    // Similarly, we'll maintain both our internal state and any user-provided\n    // state.\n    onStateChange: updater => {\n      setState(updater);\n      options.onStateChange == null || options.onStateChange(updater);\n    }\n  }));\n  return tableRef.current;\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-table/build/lib/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@tanstack/table-core/build/lib/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColumnFaceting: () => (/* binding */ ColumnFaceting),\n/* harmony export */   ColumnFiltering: () => (/* binding */ ColumnFiltering),\n/* harmony export */   ColumnGrouping: () => (/* binding */ ColumnGrouping),\n/* harmony export */   ColumnOrdering: () => (/* binding */ ColumnOrdering),\n/* harmony export */   ColumnPinning: () => (/* binding */ ColumnPinning),\n/* harmony export */   ColumnSizing: () => (/* binding */ ColumnSizing),\n/* harmony export */   ColumnVisibility: () => (/* binding */ ColumnVisibility),\n/* harmony export */   GlobalFaceting: () => (/* binding */ GlobalFaceting),\n/* harmony export */   GlobalFiltering: () => (/* binding */ GlobalFiltering),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   RowExpanding: () => (/* binding */ RowExpanding),\n/* harmony export */   RowPagination: () => (/* binding */ RowPagination),\n/* harmony export */   RowPinning: () => (/* binding */ RowPinning),\n/* harmony export */   RowSelection: () => (/* binding */ RowSelection),\n/* harmony export */   RowSorting: () => (/* binding */ RowSorting),\n/* harmony export */   _getVisibleLeafColumns: () => (/* binding */ _getVisibleLeafColumns),\n/* harmony export */   aggregationFns: () => (/* binding */ aggregationFns),\n/* harmony export */   buildHeaderGroups: () => (/* binding */ buildHeaderGroups),\n/* harmony export */   createCell: () => (/* binding */ createCell),\n/* harmony export */   createColumn: () => (/* binding */ createColumn),\n/* harmony export */   createColumnHelper: () => (/* binding */ createColumnHelper),\n/* harmony export */   createRow: () => (/* binding */ createRow),\n/* harmony export */   createTable: () => (/* binding */ createTable),\n/* harmony export */   defaultColumnSizing: () => (/* binding */ defaultColumnSizing),\n/* harmony export */   expandRows: () => (/* binding */ expandRows),\n/* harmony export */   filterFns: () => (/* binding */ filterFns),\n/* harmony export */   flattenBy: () => (/* binding */ flattenBy),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   getCoreRowModel: () => (/* binding */ getCoreRowModel),\n/* harmony export */   getExpandedRowModel: () => (/* binding */ getExpandedRowModel),\n/* harmony export */   getFacetedMinMaxValues: () => (/* binding */ getFacetedMinMaxValues),\n/* harmony export */   getFacetedRowModel: () => (/* binding */ getFacetedRowModel),\n/* harmony export */   getFacetedUniqueValues: () => (/* binding */ getFacetedUniqueValues),\n/* harmony export */   getFilteredRowModel: () => (/* binding */ getFilteredRowModel),\n/* harmony export */   getGroupedRowModel: () => (/* binding */ getGroupedRowModel),\n/* harmony export */   getMemoOptions: () => (/* binding */ getMemoOptions),\n/* harmony export */   getPaginationRowModel: () => (/* binding */ getPaginationRowModel),\n/* harmony export */   getSortedRowModel: () => (/* binding */ getSortedRowModel),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isNumberArray: () => (/* binding */ isNumberArray),\n/* harmony export */   isRowSelected: () => (/* binding */ isRowSelected),\n/* harmony export */   isSubRowSelected: () => (/* binding */ isSubRowSelected),\n/* harmony export */   makeStateUpdater: () => (/* binding */ makeStateUpdater),\n/* harmony export */   memo: () => (/* binding */ memo),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   orderColumns: () => (/* binding */ orderColumns),\n/* harmony export */   passiveEventSupported: () => (/* binding */ passiveEventSupported),\n/* harmony export */   reSplitAlphaNumeric: () => (/* binding */ reSplitAlphaNumeric),\n/* harmony export */   selectRowsFn: () => (/* binding */ selectRowsFn),\n/* harmony export */   shouldAutoRemoveFilter: () => (/* binding */ shouldAutoRemoveFilter),\n/* harmony export */   sortingFns: () => (/* binding */ sortingFns)\n/* harmony export */ });\n/**\n   * table-core\n   *\n   * Copyright (c) TanStack\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE.md file in the root directory of this source tree.\n   *\n   * @license MIT\n   */\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nfunction createColumnHelper() {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function' ? {\n        ...column,\n        accessorFn: accessor\n      } : {\n        ...column,\n        accessorKey: accessor\n      };\n    },\n    display: column => column,\n    group: column => column\n  };\n}\n\n// Is this type a tuple?\n\n// If this type is a tuple, what indices are allowed?\n\n///\n\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction noop() {\n  //\n}\nfunction makeStateUpdater(key, instance) {\n  return updater => {\n    instance.setState(old => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, old[key])\n      };\n    });\n  };\n}\nfunction isFunction(d) {\n  return d instanceof Function;\n}\nfunction isNumberArray(d) {\n  return Array.isArray(d) && d.every(val => typeof val === 'number');\n}\nfunction flattenBy(arr, getChildren) {\n  const flat = [];\n  const recurse = subArr => {\n    subArr.forEach(item => {\n      flat.push(item);\n      const children = getChildren(item);\n      if (children != null && children.length) {\n        recurse(children);\n      }\n    });\n  };\n  recurse(arr);\n  return flat;\n}\nfunction memo(getDeps, fn, opts) {\n  let deps = [];\n  let result;\n  return depArgs => {\n    let depTime;\n    if (opts.key && opts.debug) depTime = Date.now();\n    const newDeps = getDeps(depArgs);\n    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);\n    if (!depsChanged) {\n      return result;\n    }\n    deps = newDeps;\n    let resultTime;\n    if (opts.key && opts.debug) resultTime = Date.now();\n    result = fn(...newDeps);\n    opts == null || opts.onChange == null || opts.onChange(result);\n    if (opts.key && opts.debug) {\n      if (opts != null && opts.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;\n        const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;\n        const resultFpsPercentage = resultEndTime / 16;\n        const pad = (str, num) => {\n          str = String(str);\n          while (str.length < num) {\n            str = ' ' + str;\n          }\n          return str;\n        };\n        console.info(`%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`, `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0, Math.min(120 - 120 * resultFpsPercentage, 120))}deg 100% 31%);`, opts == null ? void 0 : opts.key);\n      }\n    }\n    return result;\n  };\n}\nfunction getMemoOptions(tableOptions, debugLevel, key, onChange) {\n  return {\n    debug: () => {\n      var _tableOptions$debugAl;\n      return (_tableOptions$debugAl = tableOptions == null ? void 0 : tableOptions.debugAll) != null ? _tableOptions$debugAl : tableOptions[debugLevel];\n    },\n    key:  true && key,\n    onChange\n  };\n}\n\nfunction createCell(table, row, column, columnId) {\n  const getRenderValue = () => {\n    var _cell$getValue;\n    return (_cell$getValue = cell.getValue()) != null ? _cell$getValue : table.options.renderFallbackValue;\n  };\n  const cell = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(() => [table, column, row, cell], (table, column, row, cell) => ({\n      table,\n      column,\n      row,\n      cell: cell,\n      getValue: cell.getValue,\n      renderValue: cell.renderValue\n    }), getMemoOptions(table.options, 'debugCells', 'cell.getContext'))\n  };\n  table._features.forEach(feature => {\n    feature.createCell == null || feature.createCell(cell, column, row, table);\n  }, {});\n  return cell;\n}\n\nfunction createColumn(table, columnDef, depth, parent) {\n  var _ref, _resolvedColumnDef$id;\n  const defaultColumn = table._getDefaultColumnDef();\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef\n  };\n  const accessorKey = resolvedColumnDef.accessorKey;\n  let id = (_ref = (_resolvedColumnDef$id = resolvedColumnDef.id) != null ? _resolvedColumnDef$id : accessorKey ? typeof String.prototype.replaceAll === 'function' ? accessorKey.replaceAll('.', '_') : accessorKey.replace(/\\./g, '_') : undefined) != null ? _ref : typeof resolvedColumnDef.header === 'string' ? resolvedColumnDef.header : undefined;\n  let accessorFn;\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn;\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = originalRow => {\n        let result = originalRow;\n        for (const key of accessorKey.split('.')) {\n          var _result;\n          result = (_result = result) == null ? void 0 : _result[key];\n          if ( true && result === undefined) {\n            console.warn(`\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`);\n          }\n        }\n        return result;\n      };\n    } else {\n      accessorFn = originalRow => originalRow[resolvedColumnDef.accessorKey];\n    }\n  }\n  if (!id) {\n    if (true) {\n      throw new Error(resolvedColumnDef.accessorFn ? `Columns require an id when using an accessorFn` : `Columns require an id when using a non-string header`);\n    }\n    throw new Error();\n  }\n  let column = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent,\n    depth,\n    columnDef: resolvedColumnDef,\n    columns: [],\n    getFlatColumns: memo(() => [true], () => {\n      var _column$columns;\n      return [column, ...((_column$columns = column.columns) == null ? void 0 : _column$columns.flatMap(d => d.getFlatColumns()))];\n    }, getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')),\n    getLeafColumns: memo(() => [table._getOrderColumnsFn()], orderColumns => {\n      var _column$columns2;\n      if ((_column$columns2 = column.columns) != null && _column$columns2.length) {\n        let leafColumns = column.columns.flatMap(column => column.getLeafColumns());\n        return orderColumns(leafColumns);\n      }\n      return [column];\n    }, getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns'))\n  };\n  for (const feature of table._features) {\n    feature.createColumn == null || feature.createColumn(column, table);\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column;\n}\n\nconst debug = 'debugHeaders';\n//\n\nfunction createHeader(table, column, options) {\n  var _options$id;\n  const id = (_options$id = options.id) != null ? _options$id : column.id;\n  let header = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null,\n    getLeafHeaders: () => {\n      const leafHeaders = [];\n      const recurseHeader = h => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader);\n        }\n        leafHeaders.push(h);\n      };\n      recurseHeader(header);\n      return leafHeaders;\n    },\n    getContext: () => ({\n      table,\n      header: header,\n      column\n    })\n  };\n  table._features.forEach(feature => {\n    feature.createHeader == null || feature.createHeader(header, table);\n  });\n  return header;\n}\nconst Headers = {\n  createTable: table => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, leafColumns, left, right) => {\n      var _left$map$filter, _right$map$filter;\n      const leftColumns = (_left$map$filter = left == null ? void 0 : left.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _left$map$filter : [];\n      const rightColumns = (_right$map$filter = right == null ? void 0 : right.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _right$map$filter : [];\n      const centerColumns = leafColumns.filter(column => !(left != null && left.includes(column.id)) && !(right != null && right.includes(column.id)));\n      const headerGroups = buildHeaderGroups(allColumns, [...leftColumns, ...centerColumns, ...rightColumns], table);\n      return headerGroups;\n    }, getMemoOptions(table.options, debug, 'getHeaderGroups'));\n    table.getCenterHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, leafColumns, left, right) => {\n      leafColumns = leafColumns.filter(column => !(left != null && left.includes(column.id)) && !(right != null && right.includes(column.id)));\n      return buildHeaderGroups(allColumns, leafColumns, table, 'center');\n    }, getMemoOptions(table.options, debug, 'getCenterHeaderGroups'));\n    table.getLeftHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left], (allColumns, leafColumns, left) => {\n      var _left$map$filter2;\n      const orderedLeafColumns = (_left$map$filter2 = left == null ? void 0 : left.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _left$map$filter2 : [];\n      return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left');\n    }, getMemoOptions(table.options, debug, 'getLeftHeaderGroups'));\n    table.getRightHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.right], (allColumns, leafColumns, right) => {\n      var _right$map$filter2;\n      const orderedLeafColumns = (_right$map$filter2 = right == null ? void 0 : right.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _right$map$filter2 : [];\n      return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right');\n    }, getMemoOptions(table.options, debug, 'getRightHeaderGroups'));\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(() => [table.getHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getFooterGroups'));\n    table.getLeftFooterGroups = memo(() => [table.getLeftHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getLeftFooterGroups'));\n    table.getCenterFooterGroups = memo(() => [table.getCenterHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getCenterFooterGroups'));\n    table.getRightFooterGroups = memo(() => [table.getRightHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getRightFooterGroups'));\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(() => [table.getHeaderGroups()], headerGroups => {\n      return headerGroups.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getFlatHeaders'));\n    table.getLeftFlatHeaders = memo(() => [table.getLeftHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getLeftFlatHeaders'));\n    table.getCenterFlatHeaders = memo(() => [table.getCenterHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getCenterFlatHeaders'));\n    table.getRightFlatHeaders = memo(() => [table.getRightHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getRightFlatHeaders'));\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(() => [table.getCenterFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders;\n        return !((_header$subHeaders = header.subHeaders) != null && _header$subHeaders.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getCenterLeafHeaders'));\n    table.getLeftLeafHeaders = memo(() => [table.getLeftFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders2;\n        return !((_header$subHeaders2 = header.subHeaders) != null && _header$subHeaders2.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getLeftLeafHeaders'));\n    table.getRightLeafHeaders = memo(() => [table.getRightFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders3;\n        return !((_header$subHeaders3 = header.subHeaders) != null && _header$subHeaders3.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getRightLeafHeaders'));\n    table.getLeafHeaders = memo(() => [table.getLeftHeaderGroups(), table.getCenterHeaderGroups(), table.getRightHeaderGroups()], (left, center, right) => {\n      var _left$0$headers, _left$, _center$0$headers, _center$, _right$0$headers, _right$;\n      return [...((_left$0$headers = (_left$ = left[0]) == null ? void 0 : _left$.headers) != null ? _left$0$headers : []), ...((_center$0$headers = (_center$ = center[0]) == null ? void 0 : _center$.headers) != null ? _center$0$headers : []), ...((_right$0$headers = (_right$ = right[0]) == null ? void 0 : _right$.headers) != null ? _right$0$headers : [])].map(header => {\n        return header.getLeafHeaders();\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getLeafHeaders'));\n  }\n};\nfunction buildHeaderGroups(allColumns, columnsToGroup, table, headerFamily) {\n  var _headerGroups$0$heade, _headerGroups$;\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0;\n  const findMaxDepth = function (columns, depth) {\n    if (depth === void 0) {\n      depth = 1;\n    }\n    maxDepth = Math.max(maxDepth, depth);\n    columns.filter(column => column.getIsVisible()).forEach(column => {\n      var _column$columns;\n      if ((_column$columns = column.columns) != null && _column$columns.length) {\n        findMaxDepth(column.columns, depth + 1);\n      }\n    }, 0);\n  };\n  findMaxDepth(allColumns);\n  let headerGroups = [];\n  const createHeaderGroup = (headersToGroup, depth) => {\n    // The header group we are creating\n    const headerGroup = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: []\n    };\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders = [];\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0];\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth;\n      let column;\n      let isPlaceholder = false;\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent;\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column;\n        isPlaceholder = true;\n      }\n      if (latestPendingParentHeader && (latestPendingParentHeader == null ? void 0 : latestPendingParentHeader.column) === column) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup);\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup == null ? void 0 : headerToGroup.id].filter(Boolean).join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder ? `${pendingParentHeaders.filter(d => d.column === column).length}` : undefined,\n          depth,\n          index: pendingParentHeaders.length\n        });\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup);\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header);\n      }\n      headerGroup.headers.push(headerToGroup);\n      headerToGroup.headerGroup = headerGroup;\n    });\n    headerGroups.push(headerGroup);\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1);\n    }\n  };\n  const bottomHeaders = columnsToGroup.map((column, index) => createHeader(table, column, {\n    depth: maxDepth,\n    index\n  }));\n  createHeaderGroup(bottomHeaders, maxDepth - 1);\n  headerGroups.reverse();\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = headers => {\n    const filteredHeaders = headers.filter(header => header.column.getIsVisible());\n    return filteredHeaders.map(header => {\n      let colSpan = 0;\n      let rowSpan = 0;\n      let childRowSpans = [0];\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = [];\n        recurseHeadersForSpans(header.subHeaders).forEach(_ref => {\n          let {\n            colSpan: childColSpan,\n            rowSpan: childRowSpan\n          } = _ref;\n          colSpan += childColSpan;\n          childRowSpans.push(childRowSpan);\n        });\n      } else {\n        colSpan = 1;\n      }\n      const minChildRowSpan = Math.min(...childRowSpans);\n      rowSpan = rowSpan + minChildRowSpan;\n      header.colSpan = colSpan;\n      header.rowSpan = rowSpan;\n      return {\n        colSpan,\n        rowSpan\n      };\n    });\n  };\n  recurseHeadersForSpans((_headerGroups$0$heade = (_headerGroups$ = headerGroups[0]) == null ? void 0 : _headerGroups$.headers) != null ? _headerGroups$0$heade : []);\n  return headerGroups;\n}\n\nconst createRow = (table, id, original, rowIndex, depth, subRows, parentId) => {\n  let row = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.accessorFn)) {\n        return undefined;\n      }\n      row._valuesCache[columnId] = column.accessorFn(row.original, rowIndex);\n      return row._valuesCache[columnId];\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.accessorFn)) {\n        return undefined;\n      }\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)];\n        return row._uniqueValuesCache[columnId];\n      }\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(row.original, rowIndex);\n      return row._uniqueValuesCache[columnId];\n    },\n    renderValue: columnId => {\n      var _row$getValue;\n      return (_row$getValue = row.getValue(columnId)) != null ? _row$getValue : table.options.renderFallbackValue;\n    },\n    subRows: subRows != null ? subRows : [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () => row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows = [];\n      let currentRow = row;\n      while (true) {\n        const parentRow = currentRow.getParentRow();\n        if (!parentRow) break;\n        parentRows.push(parentRow);\n        currentRow = parentRow;\n      }\n      return parentRows.reverse();\n    },\n    getAllCells: memo(() => [table.getAllLeafColumns()], leafColumns => {\n      return leafColumns.map(column => {\n        return createCell(table, row, column, column.id);\n      });\n    }, getMemoOptions(table.options, 'debugRows', 'getAllCells')),\n    _getAllCellsByColumnId: memo(() => [row.getAllCells()], allCells => {\n      return allCells.reduce((acc, cell) => {\n        acc[cell.column.id] = cell;\n        return acc;\n      }, {});\n    }, getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId'))\n  };\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i];\n    feature == null || feature.createRow == null || feature.createRow(row, table);\n  }\n  return row;\n};\n\n//\n\nconst ColumnFaceting = {\n  createColumn: (column, table) => {\n    column._getFacetedRowModel = table.options.getFacetedRowModel && table.options.getFacetedRowModel(table, column.id);\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return column._getFacetedRowModel();\n    };\n    column._getFacetedUniqueValues = table.options.getFacetedUniqueValues && table.options.getFacetedUniqueValues(table, column.id);\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map();\n      }\n      return column._getFacetedUniqueValues();\n    };\n    column._getFacetedMinMaxValues = table.options.getFacetedMinMaxValues && table.options.getFacetedMinMaxValues(table, column.id);\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined;\n      }\n      return column._getFacetedMinMaxValues();\n    };\n  }\n};\n\nconst includesString = (row, columnId, filterValue) => {\n  var _filterValue$toString, _row$getValue;\n  const search = filterValue == null || (_filterValue$toString = filterValue.toString()) == null ? void 0 : _filterValue$toString.toLowerCase();\n  return Boolean((_row$getValue = row.getValue(columnId)) == null || (_row$getValue = _row$getValue.toString()) == null || (_row$getValue = _row$getValue.toLowerCase()) == null ? void 0 : _row$getValue.includes(search));\n};\nincludesString.autoRemove = val => testFalsey(val);\nconst includesStringSensitive = (row, columnId, filterValue) => {\n  var _row$getValue2;\n  return Boolean((_row$getValue2 = row.getValue(columnId)) == null || (_row$getValue2 = _row$getValue2.toString()) == null ? void 0 : _row$getValue2.includes(filterValue));\n};\nincludesStringSensitive.autoRemove = val => testFalsey(val);\nconst equalsString = (row, columnId, filterValue) => {\n  var _row$getValue3;\n  return ((_row$getValue3 = row.getValue(columnId)) == null || (_row$getValue3 = _row$getValue3.toString()) == null ? void 0 : _row$getValue3.toLowerCase()) === (filterValue == null ? void 0 : filterValue.toLowerCase());\n};\nequalsString.autoRemove = val => testFalsey(val);\nconst arrIncludes = (row, columnId, filterValue) => {\n  var _row$getValue4;\n  return (_row$getValue4 = row.getValue(columnId)) == null ? void 0 : _row$getValue4.includes(filterValue);\n};\narrIncludes.autoRemove = val => testFalsey(val);\nconst arrIncludesAll = (row, columnId, filterValue) => {\n  return !filterValue.some(val => {\n    var _row$getValue5;\n    return !((_row$getValue5 = row.getValue(columnId)) != null && _row$getValue5.includes(val));\n  });\n};\narrIncludesAll.autoRemove = val => testFalsey(val) || !(val != null && val.length);\nconst arrIncludesSome = (row, columnId, filterValue) => {\n  return filterValue.some(val => {\n    var _row$getValue6;\n    return (_row$getValue6 = row.getValue(columnId)) == null ? void 0 : _row$getValue6.includes(val);\n  });\n};\narrIncludesSome.autoRemove = val => testFalsey(val) || !(val != null && val.length);\nconst equals = (row, columnId, filterValue) => {\n  return row.getValue(columnId) === filterValue;\n};\nequals.autoRemove = val => testFalsey(val);\nconst weakEquals = (row, columnId, filterValue) => {\n  return row.getValue(columnId) == filterValue;\n};\nweakEquals.autoRemove = val => testFalsey(val);\nconst inNumberRange = (row, columnId, filterValue) => {\n  let [min, max] = filterValue;\n  const rowValue = row.getValue(columnId);\n  return rowValue >= min && rowValue <= max;\n};\ninNumberRange.resolveFilterValue = val => {\n  let [unsafeMin, unsafeMax] = val;\n  let parsedMin = typeof unsafeMin !== 'number' ? parseFloat(unsafeMin) : unsafeMin;\n  let parsedMax = typeof unsafeMax !== 'number' ? parseFloat(unsafeMax) : unsafeMax;\n  let min = unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin;\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax;\n  if (min > max) {\n    const temp = min;\n    min = max;\n    max = temp;\n  }\n  return [min, max];\n};\ninNumberRange.autoRemove = val => testFalsey(val) || testFalsey(val[0]) && testFalsey(val[1]);\n\n// Export\n\nconst filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange\n};\n// Utils\n\nfunction testFalsey(val) {\n  return val === undefined || val === null || val === '';\n}\n\n//\n\nconst ColumnFiltering = {\n  getDefaultColumnDef: () => {\n    return {\n      filterFn: 'auto'\n    };\n  },\n  getInitialState: state => {\n    return {\n      columnFilters: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100\n    };\n  },\n  createColumn: (column, table) => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'string') {\n        return filterFns.includesString;\n      }\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange;\n      }\n      if (typeof value === 'boolean') {\n        return filterFns.equals;\n      }\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals;\n      }\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes;\n      }\n      return filterFns.weakEquals;\n    };\n    column.getFilterFn = () => {\n      var _table$options$filter, _table$options$filter2;\n      return isFunction(column.columnDef.filterFn) ? column.columnDef.filterFn : column.columnDef.filterFn === 'auto' ? column.getAutoFilterFn() : // @ts-ignore\n      (_table$options$filter = (_table$options$filter2 = table.options.filterFns) == null ? void 0 : _table$options$filter2[column.columnDef.filterFn]) != null ? _table$options$filter : filterFns[column.columnDef.filterFn];\n    };\n    column.getCanFilter = () => {\n      var _column$columnDef$ena, _table$options$enable, _table$options$enable2;\n      return ((_column$columnDef$ena = column.columnDef.enableColumnFilter) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableColumnFilters) != null ? _table$options$enable : true) && ((_table$options$enable2 = table.options.enableFilters) != null ? _table$options$enable2 : true) && !!column.accessorFn;\n    };\n    column.getIsFiltered = () => column.getFilterIndex() > -1;\n    column.getFilterValue = () => {\n      var _table$getState$colum;\n      return (_table$getState$colum = table.getState().columnFilters) == null || (_table$getState$colum = _table$getState$colum.find(d => d.id === column.id)) == null ? void 0 : _table$getState$colum.value;\n    };\n    column.getFilterIndex = () => {\n      var _table$getState$colum2, _table$getState$colum3;\n      return (_table$getState$colum2 = (_table$getState$colum3 = table.getState().columnFilters) == null ? void 0 : _table$getState$colum3.findIndex(d => d.id === column.id)) != null ? _table$getState$colum2 : -1;\n    };\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn();\n        const previousFilter = old == null ? void 0 : old.find(d => d.id === column.id);\n        const newFilter = functionalUpdate(value, previousFilter ? previousFilter.value : undefined);\n\n        //\n        if (shouldAutoRemoveFilter(filterFn, newFilter, column)) {\n          var _old$filter;\n          return (_old$filter = old == null ? void 0 : old.filter(d => d.id !== column.id)) != null ? _old$filter : [];\n        }\n        const newFilterObj = {\n          id: column.id,\n          value: newFilter\n        };\n        if (previousFilter) {\n          var _old$map;\n          return (_old$map = old == null ? void 0 : old.map(d => {\n            if (d.id === column.id) {\n              return newFilterObj;\n            }\n            return d;\n          })) != null ? _old$map : [];\n        }\n        if (old != null && old.length) {\n          return [...old, newFilterObj];\n        }\n        return [newFilterObj];\n      });\n    };\n  },\n  createRow: (row, _table) => {\n    row.columnFilters = {};\n    row.columnFiltersMeta = {};\n  },\n  createTable: table => {\n    table.setColumnFilters = updater => {\n      const leafColumns = table.getAllLeafColumns();\n      const updateFn = old => {\n        var _functionalUpdate;\n        return (_functionalUpdate = functionalUpdate(updater, old)) == null ? void 0 : _functionalUpdate.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id);\n          if (column) {\n            const filterFn = column.getFilterFn();\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false;\n            }\n          }\n          return true;\n        });\n      };\n      table.options.onColumnFiltersChange == null || table.options.onColumnFiltersChange(updateFn);\n    };\n    table.resetColumnFilters = defaultState => {\n      var _table$initialState$c, _table$initialState;\n      table.setColumnFilters(defaultState ? [] : (_table$initialState$c = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.columnFilters) != null ? _table$initialState$c : []);\n    };\n    table.getPreFilteredRowModel = () => table.getCoreRowModel();\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table);\n      }\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return table._getFilteredRowModel();\n    };\n  }\n};\nfunction shouldAutoRemoveFilter(filterFn, value, column) {\n  return (filterFn && filterFn.autoRemove ? filterFn.autoRemove(value, column) : false) || typeof value === 'undefined' || typeof value === 'string' && !value;\n}\n\nconst sum = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId);\n    return sum + (typeof nextValue === 'number' ? nextValue : 0);\n  }, 0);\n};\nconst min = (columnId, _leafRows, childRows) => {\n  let min;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null && (min > value || min === undefined && value >= value)) {\n      min = value;\n    }\n  });\n  return min;\n};\nconst max = (columnId, _leafRows, childRows) => {\n  let max;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null && (max < value || max === undefined && value >= value)) {\n      max = value;\n    }\n  });\n  return max;\n};\nconst extent = (columnId, _leafRows, childRows) => {\n  let min;\n  let max;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value;\n      } else {\n        if (min > value) min = value;\n        if (max < value) max = value;\n      }\n    }\n  });\n  return [min, max];\n};\nconst mean = (columnId, leafRows) => {\n  let count = 0;\n  let sum = 0;\n  leafRows.forEach(row => {\n    let value = row.getValue(columnId);\n    if (value != null && (value = +value) >= value) {\n      ++count, sum += value;\n    }\n  });\n  if (count) return sum / count;\n  return;\n};\nconst median = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return;\n  }\n  const values = leafRows.map(row => row.getValue(columnId));\n  if (!isNumberArray(values)) {\n    return;\n  }\n  if (values.length === 1) {\n    return values[0];\n  }\n  const mid = Math.floor(values.length / 2);\n  const nums = values.sort((a, b) => a - b);\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1] + nums[mid]) / 2;\n};\nconst unique = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values());\n};\nconst uniqueCount = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size;\n};\nconst count = (_columnId, leafRows) => {\n  return leafRows.length;\n};\nconst aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count\n};\n\n//\n\nconst ColumnGrouping = {\n  getDefaultColumnDef: () => {\n    return {\n      aggregatedCell: props => {\n        var _toString, _props$getValue;\n        return (_toString = (_props$getValue = props.getValue()) == null || _props$getValue.toString == null ? void 0 : _props$getValue.toString()) != null ? _toString : null;\n      },\n      aggregationFn: 'auto'\n    };\n  },\n  getInitialState: state => {\n    return {\n      grouping: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder'\n    };\n  },\n  createColumn: (column, table) => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old != null && old.includes(column.id)) {\n          return old.filter(d => d !== column.id);\n        }\n        return [...(old != null ? old : []), column.id];\n      });\n    };\n    column.getCanGroup = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableGrouping) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableGrouping) != null ? _table$options$enable : true) && (!!column.accessorFn || !!column.columnDef.getGroupingValue);\n    };\n    column.getIsGrouped = () => {\n      var _table$getState$group;\n      return (_table$getState$group = table.getState().grouping) == null ? void 0 : _table$getState$group.includes(column.id);\n    };\n    column.getGroupedIndex = () => {\n      var _table$getState$group2;\n      return (_table$getState$group2 = table.getState().grouping) == null ? void 0 : _table$getState$group2.indexOf(column.id);\n    };\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup();\n      return () => {\n        if (!canGroup) return;\n        column.toggleGrouping();\n      };\n    };\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'number') {\n        return aggregationFns.sum;\n      }\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent;\n      }\n    };\n    column.getAggregationFn = () => {\n      var _table$options$aggreg, _table$options$aggreg2;\n      if (!column) {\n        throw new Error();\n      }\n      return isFunction(column.columnDef.aggregationFn) ? column.columnDef.aggregationFn : column.columnDef.aggregationFn === 'auto' ? column.getAutoAggregationFn() : (_table$options$aggreg = (_table$options$aggreg2 = table.options.aggregationFns) == null ? void 0 : _table$options$aggreg2[column.columnDef.aggregationFn]) != null ? _table$options$aggreg : aggregationFns[column.columnDef.aggregationFn];\n    };\n  },\n  createTable: table => {\n    table.setGrouping = updater => table.options.onGroupingChange == null ? void 0 : table.options.onGroupingChange(updater);\n    table.resetGrouping = defaultState => {\n      var _table$initialState$g, _table$initialState;\n      table.setGrouping(defaultState ? [] : (_table$initialState$g = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.grouping) != null ? _table$initialState$g : []);\n    };\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel();\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table);\n      }\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel();\n      }\n      return table._getGroupedRowModel();\n    };\n  },\n  createRow: (row, table) => {\n    row.getIsGrouped = () => !!row.groupingColumnId;\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.columnDef.getGroupingValue)) {\n        return row.getValue(columnId);\n      }\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(row.original);\n      return row._groupingValuesCache[columnId];\n    };\n    row._groupingValuesCache = {};\n  },\n  createCell: (cell, column, row, table) => {\n    cell.getIsGrouped = () => column.getIsGrouped() && column.id === row.groupingColumnId;\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped();\n    cell.getIsAggregated = () => {\n      var _row$subRows;\n      return !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!((_row$subRows = row.subRows) != null && _row$subRows.length);\n    };\n  }\n};\nfunction orderColumns(leafColumns, grouping, groupedColumnMode) {\n  if (!(grouping != null && grouping.length) || !groupedColumnMode) {\n    return leafColumns;\n  }\n  const nonGroupingColumns = leafColumns.filter(col => !grouping.includes(col.id));\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns;\n  }\n  const groupingColumns = grouping.map(g => leafColumns.find(col => col.id === g)).filter(Boolean);\n  return [...groupingColumns, ...nonGroupingColumns];\n}\n\n//\n\nconst ColumnOrdering = {\n  getInitialState: state => {\n    return {\n      columnOrder: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.getIndex = memo(position => [_getVisibleLeafColumns(table, position)], columns => columns.findIndex(d => d.id === column.id), getMemoOptions(table.options, 'debugColumns', 'getIndex'));\n    column.getIsFirstColumn = position => {\n      var _columns$;\n      const columns = _getVisibleLeafColumns(table, position);\n      return ((_columns$ = columns[0]) == null ? void 0 : _columns$.id) === column.id;\n    };\n    column.getIsLastColumn = position => {\n      var _columns;\n      const columns = _getVisibleLeafColumns(table, position);\n      return ((_columns = columns[columns.length - 1]) == null ? void 0 : _columns.id) === column.id;\n    };\n  },\n  createTable: table => {\n    table.setColumnOrder = updater => table.options.onColumnOrderChange == null ? void 0 : table.options.onColumnOrderChange(updater);\n    table.resetColumnOrder = defaultState => {\n      var _table$initialState$c;\n      table.setColumnOrder(defaultState ? [] : (_table$initialState$c = table.initialState.columnOrder) != null ? _table$initialState$c : []);\n    };\n    table._getOrderColumnsFn = memo(() => [table.getState().columnOrder, table.getState().grouping, table.options.groupedColumnMode], (columnOrder, grouping, groupedColumnMode) => columns => {\n      // Sort grouped columns to the start of the column list\n      // before the headers are built\n      let orderedColumns = [];\n\n      // If there is no order, return the normal columns\n      if (!(columnOrder != null && columnOrder.length)) {\n        orderedColumns = columns;\n      } else {\n        const columnOrderCopy = [...columnOrder];\n\n        // If there is an order, make a copy of the columns\n        const columnsCopy = [...columns];\n\n        // And make a new ordered array of the columns\n\n        // Loop over the columns and place them in order into the new array\n        while (columnsCopy.length && columnOrderCopy.length) {\n          const targetColumnId = columnOrderCopy.shift();\n          const foundIndex = columnsCopy.findIndex(d => d.id === targetColumnId);\n          if (foundIndex > -1) {\n            orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]);\n          }\n        }\n\n        // If there are any columns left, add them to the end\n        orderedColumns = [...orderedColumns, ...columnsCopy];\n      }\n      return orderColumns(orderedColumns, grouping, groupedColumnMode);\n    }, getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn'));\n  }\n};\n\n//\n\nconst getDefaultColumnPinningState = () => ({\n  left: [],\n  right: []\n});\nconst ColumnPinning = {\n  getInitialState: state => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.pin = position => {\n      const columnIds = column.getLeafColumns().map(d => d.id).filter(Boolean);\n      table.setColumnPinning(old => {\n        var _old$left3, _old$right3;\n        if (position === 'right') {\n          var _old$left, _old$right;\n          return {\n            left: ((_old$left = old == null ? void 0 : old.left) != null ? _old$left : []).filter(d => !(columnIds != null && columnIds.includes(d))),\n            right: [...((_old$right = old == null ? void 0 : old.right) != null ? _old$right : []).filter(d => !(columnIds != null && columnIds.includes(d))), ...columnIds]\n          };\n        }\n        if (position === 'left') {\n          var _old$left2, _old$right2;\n          return {\n            left: [...((_old$left2 = old == null ? void 0 : old.left) != null ? _old$left2 : []).filter(d => !(columnIds != null && columnIds.includes(d))), ...columnIds],\n            right: ((_old$right2 = old == null ? void 0 : old.right) != null ? _old$right2 : []).filter(d => !(columnIds != null && columnIds.includes(d)))\n          };\n        }\n        return {\n          left: ((_old$left3 = old == null ? void 0 : old.left) != null ? _old$left3 : []).filter(d => !(columnIds != null && columnIds.includes(d))),\n          right: ((_old$right3 = old == null ? void 0 : old.right) != null ? _old$right3 : []).filter(d => !(columnIds != null && columnIds.includes(d)))\n        };\n      });\n    };\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns();\n      return leafColumns.some(d => {\n        var _d$columnDef$enablePi, _ref, _table$options$enable;\n        return ((_d$columnDef$enablePi = d.columnDef.enablePinning) != null ? _d$columnDef$enablePi : true) && ((_ref = (_table$options$enable = table.options.enableColumnPinning) != null ? _table$options$enable : table.options.enablePinning) != null ? _ref : true);\n      });\n    };\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id);\n      const {\n        left,\n        right\n      } = table.getState().columnPinning;\n      const isLeft = leafColumnIds.some(d => left == null ? void 0 : left.includes(d));\n      const isRight = leafColumnIds.some(d => right == null ? void 0 : right.includes(d));\n      return isLeft ? 'left' : isRight ? 'right' : false;\n    };\n    column.getPinnedIndex = () => {\n      var _table$getState$colum, _table$getState$colum2;\n      const position = column.getIsPinned();\n      return position ? (_table$getState$colum = (_table$getState$colum2 = table.getState().columnPinning) == null || (_table$getState$colum2 = _table$getState$colum2[position]) == null ? void 0 : _table$getState$colum2.indexOf(column.id)) != null ? _table$getState$colum : -1 : 0;\n    };\n  },\n  createRow: (row, table) => {\n    row.getCenterVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allCells, left, right) => {\n      const leftAndRight = [...(left != null ? left : []), ...(right != null ? right : [])];\n      return allCells.filter(d => !leftAndRight.includes(d.column.id));\n    }, getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells'));\n    row.getLeftVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.left], (allCells, left) => {\n      const cells = (left != null ? left : []).map(columnId => allCells.find(cell => cell.column.id === columnId)).filter(Boolean).map(d => ({\n        ...d,\n        position: 'left'\n      }));\n      return cells;\n    }, getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells'));\n    row.getRightVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.right], (allCells, right) => {\n      const cells = (right != null ? right : []).map(columnId => allCells.find(cell => cell.column.id === columnId)).filter(Boolean).map(d => ({\n        ...d,\n        position: 'right'\n      }));\n      return cells;\n    }, getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells'));\n  },\n  createTable: table => {\n    table.setColumnPinning = updater => table.options.onColumnPinningChange == null ? void 0 : table.options.onColumnPinningChange(updater);\n    table.resetColumnPinning = defaultState => {\n      var _table$initialState$c, _table$initialState;\n      return table.setColumnPinning(defaultState ? getDefaultColumnPinningState() : (_table$initialState$c = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.columnPinning) != null ? _table$initialState$c : getDefaultColumnPinningState());\n    };\n    table.getIsSomeColumnsPinned = position => {\n      var _pinningState$positio;\n      const pinningState = table.getState().columnPinning;\n      if (!position) {\n        var _pinningState$left, _pinningState$right;\n        return Boolean(((_pinningState$left = pinningState.left) == null ? void 0 : _pinningState$left.length) || ((_pinningState$right = pinningState.right) == null ? void 0 : _pinningState$right.length));\n      }\n      return Boolean((_pinningState$positio = pinningState[position]) == null ? void 0 : _pinningState$positio.length);\n    };\n    table.getLeftLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.left], (allColumns, left) => {\n      return (left != null ? left : []).map(columnId => allColumns.find(column => column.id === columnId)).filter(Boolean);\n    }, getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns'));\n    table.getRightLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.right], (allColumns, right) => {\n      return (right != null ? right : []).map(columnId => allColumns.find(column => column.id === columnId)).filter(Boolean);\n    }, getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns'));\n    table.getCenterLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, left, right) => {\n      const leftAndRight = [...(left != null ? left : []), ...(right != null ? right : [])];\n      return allColumns.filter(d => !leftAndRight.includes(d.id));\n    }, getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns'));\n  }\n};\n\nfunction safelyAccessDocument(_document) {\n  return _document || (typeof document !== 'undefined' ? document : null);\n}\n\n//\n\n//\n\nconst defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER\n};\nconst getDefaultColumnSizingInfoState = () => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: []\n});\nconst ColumnSizing = {\n  getDefaultColumnDef: () => {\n    return defaultColumnSizing;\n  },\n  getInitialState: state => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.getSize = () => {\n      var _column$columnDef$min, _ref, _column$columnDef$max;\n      const columnSize = table.getState().columnSizing[column.id];\n      return Math.min(Math.max((_column$columnDef$min = column.columnDef.minSize) != null ? _column$columnDef$min : defaultColumnSizing.minSize, (_ref = columnSize != null ? columnSize : column.columnDef.size) != null ? _ref : defaultColumnSizing.size), (_column$columnDef$max = column.columnDef.maxSize) != null ? _column$columnDef$max : defaultColumnSizing.maxSize);\n    };\n    column.getStart = memo(position => [position, _getVisibleLeafColumns(table, position), table.getState().columnSizing], (position, columns) => columns.slice(0, column.getIndex(position)).reduce((sum, column) => sum + column.getSize(), 0), getMemoOptions(table.options, 'debugColumns', 'getStart'));\n    column.getAfter = memo(position => [position, _getVisibleLeafColumns(table, position), table.getState().columnSizing], (position, columns) => columns.slice(column.getIndex(position) + 1).reduce((sum, column) => sum + column.getSize(), 0), getMemoOptions(table.options, 'debugColumns', 'getAfter'));\n    column.resetSize = () => {\n      table.setColumnSizing(_ref2 => {\n        let {\n          [column.id]: _,\n          ...rest\n        } = _ref2;\n        return rest;\n      });\n    };\n    column.getCanResize = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableResizing) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableColumnResizing) != null ? _table$options$enable : true);\n    };\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id;\n    };\n  },\n  createHeader: (header, table) => {\n    header.getSize = () => {\n      let sum = 0;\n      const recurse = header => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse);\n        } else {\n          var _header$column$getSiz;\n          sum += (_header$column$getSiz = header.column.getSize()) != null ? _header$column$getSiz : 0;\n        }\n      };\n      recurse(header);\n      return sum;\n    };\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1];\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize();\n      }\n      return 0;\n    };\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id);\n      const canResize = column == null ? void 0 : column.getCanResize();\n      return e => {\n        if (!column || !canResize) {\n          return;\n        }\n        e.persist == null || e.persist();\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return;\n          }\n        }\n        const startSize = header.getSize();\n        const columnSizingStart = header ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()]) : [[column.id, column.getSize()]];\n        const clientX = isTouchStartEvent(e) ? Math.round(e.touches[0].clientX) : e.clientX;\n        const newColumnSizing = {};\n        const updateOffset = (eventType, clientXPos) => {\n          if (typeof clientXPos !== 'number') {\n            return;\n          }\n          table.setColumnSizingInfo(old => {\n            var _old$startOffset, _old$startSize;\n            const deltaDirection = table.options.columnResizeDirection === 'rtl' ? -1 : 1;\n            const deltaOffset = (clientXPos - ((_old$startOffset = old == null ? void 0 : old.startOffset) != null ? _old$startOffset : 0)) * deltaDirection;\n            const deltaPercentage = Math.max(deltaOffset / ((_old$startSize = old == null ? void 0 : old.startSize) != null ? _old$startSize : 0), -0.999999);\n            old.columnSizingStart.forEach(_ref3 => {\n              let [columnId, headerSize] = _ref3;\n              newColumnSizing[columnId] = Math.round(Math.max(headerSize + headerSize * deltaPercentage, 0) * 100) / 100;\n            });\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage\n            };\n          });\n          if (table.options.columnResizeMode === 'onChange' || eventType === 'end') {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing\n            }));\n          }\n        };\n        const onMove = clientXPos => updateOffset('move', clientXPos);\n        const onEnd = clientXPos => {\n          updateOffset('end', clientXPos);\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: []\n          }));\n        };\n        const contextDocument = safelyAccessDocument(_contextDocument);\n        const mouseEvents = {\n          moveHandler: e => onMove(e.clientX),\n          upHandler: e => {\n            contextDocument == null || contextDocument.removeEventListener('mousemove', mouseEvents.moveHandler);\n            contextDocument == null || contextDocument.removeEventListener('mouseup', mouseEvents.upHandler);\n            onEnd(e.clientX);\n          }\n        };\n        const touchEvents = {\n          moveHandler: e => {\n            if (e.cancelable) {\n              e.preventDefault();\n              e.stopPropagation();\n            }\n            onMove(e.touches[0].clientX);\n            return false;\n          },\n          upHandler: e => {\n            var _e$touches$;\n            contextDocument == null || contextDocument.removeEventListener('touchmove', touchEvents.moveHandler);\n            contextDocument == null || contextDocument.removeEventListener('touchend', touchEvents.upHandler);\n            if (e.cancelable) {\n              e.preventDefault();\n              e.stopPropagation();\n            }\n            onEnd((_e$touches$ = e.touches[0]) == null ? void 0 : _e$touches$.clientX);\n          }\n        };\n        const passiveIfSupported = passiveEventSupported() ? {\n          passive: false\n        } : false;\n        if (isTouchStartEvent(e)) {\n          contextDocument == null || contextDocument.addEventListener('touchmove', touchEvents.moveHandler, passiveIfSupported);\n          contextDocument == null || contextDocument.addEventListener('touchend', touchEvents.upHandler, passiveIfSupported);\n        } else {\n          contextDocument == null || contextDocument.addEventListener('mousemove', mouseEvents.moveHandler, passiveIfSupported);\n          contextDocument == null || contextDocument.addEventListener('mouseup', mouseEvents.upHandler, passiveIfSupported);\n        }\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id\n        }));\n      };\n    };\n  },\n  createTable: table => {\n    table.setColumnSizing = updater => table.options.onColumnSizingChange == null ? void 0 : table.options.onColumnSizingChange(updater);\n    table.setColumnSizingInfo = updater => table.options.onColumnSizingInfoChange == null ? void 0 : table.options.onColumnSizingInfoChange(updater);\n    table.resetColumnSizing = defaultState => {\n      var _table$initialState$c;\n      table.setColumnSizing(defaultState ? {} : (_table$initialState$c = table.initialState.columnSizing) != null ? _table$initialState$c : {});\n    };\n    table.resetHeaderSizeInfo = defaultState => {\n      var _table$initialState$c2;\n      table.setColumnSizingInfo(defaultState ? getDefaultColumnSizingInfoState() : (_table$initialState$c2 = table.initialState.columnSizingInfo) != null ? _table$initialState$c2 : getDefaultColumnSizingInfoState());\n    };\n    table.getTotalSize = () => {\n      var _table$getHeaderGroup, _table$getHeaderGroup2;\n      return (_table$getHeaderGroup = (_table$getHeaderGroup2 = table.getHeaderGroups()[0]) == null ? void 0 : _table$getHeaderGroup2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getHeaderGroup : 0;\n    };\n    table.getLeftTotalSize = () => {\n      var _table$getLeftHeaderG, _table$getLeftHeaderG2;\n      return (_table$getLeftHeaderG = (_table$getLeftHeaderG2 = table.getLeftHeaderGroups()[0]) == null ? void 0 : _table$getLeftHeaderG2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getLeftHeaderG : 0;\n    };\n    table.getCenterTotalSize = () => {\n      var _table$getCenterHeade, _table$getCenterHeade2;\n      return (_table$getCenterHeade = (_table$getCenterHeade2 = table.getCenterHeaderGroups()[0]) == null ? void 0 : _table$getCenterHeade2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getCenterHeade : 0;\n    };\n    table.getRightTotalSize = () => {\n      var _table$getRightHeader, _table$getRightHeader2;\n      return (_table$getRightHeader = (_table$getRightHeader2 = table.getRightHeaderGroups()[0]) == null ? void 0 : _table$getRightHeader2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getRightHeader : 0;\n    };\n  }\n};\nlet passiveSupported = null;\nfunction passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported;\n  let supported = false;\n  try {\n    const options = {\n      get passive() {\n        supported = true;\n        return false;\n      }\n    };\n    const noop = () => {};\n    window.addEventListener('test', noop, options);\n    window.removeEventListener('test', noop);\n  } catch (err) {\n    supported = false;\n  }\n  passiveSupported = supported;\n  return passiveSupported;\n}\nfunction isTouchStartEvent(e) {\n  return e.type === 'touchstart';\n}\n\n//\n\nconst ColumnVisibility = {\n  getInitialState: state => {\n    return {\n      columnVisibility: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value != null ? value : !column.getIsVisible()\n        }));\n      }\n    };\n    column.getIsVisible = () => {\n      var _ref, _table$getState$colum;\n      const childColumns = column.columns;\n      return (_ref = childColumns.length ? childColumns.some(c => c.getIsVisible()) : (_table$getState$colum = table.getState().columnVisibility) == null ? void 0 : _table$getState$colum[column.id]) != null ? _ref : true;\n    };\n    column.getCanHide = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableHiding) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableHiding) != null ? _table$options$enable : true);\n    };\n    column.getToggleVisibilityHandler = () => {\n      return e => {\n        column.toggleVisibility == null || column.toggleVisibility(e.target.checked);\n      };\n    };\n  },\n  createRow: (row, table) => {\n    row._getAllVisibleCells = memo(() => [row.getAllCells(), table.getState().columnVisibility], cells => {\n      return cells.filter(cell => cell.column.getIsVisible());\n    }, getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells'));\n    row.getVisibleCells = memo(() => [row.getLeftVisibleCells(), row.getCenterVisibleCells(), row.getRightVisibleCells()], (left, center, right) => [...left, ...center, ...right], getMemoOptions(table.options, 'debugRows', 'getVisibleCells'));\n  },\n  createTable: table => {\n    const makeVisibleColumnsMethod = (key, getColumns) => {\n      return memo(() => [getColumns(), getColumns().filter(d => d.getIsVisible()).map(d => d.id).join('_')], columns => {\n        return columns.filter(d => d.getIsVisible == null ? void 0 : d.getIsVisible());\n      }, getMemoOptions(table.options, 'debugColumns', key));\n    };\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod('getVisibleFlatColumns', () => table.getAllFlatColumns());\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod('getVisibleLeafColumns', () => table.getAllLeafColumns());\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod('getLeftVisibleLeafColumns', () => table.getLeftLeafColumns());\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod('getRightVisibleLeafColumns', () => table.getRightLeafColumns());\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod('getCenterVisibleLeafColumns', () => table.getCenterLeafColumns());\n    table.setColumnVisibility = updater => table.options.onColumnVisibilityChange == null ? void 0 : table.options.onColumnVisibilityChange(updater);\n    table.resetColumnVisibility = defaultState => {\n      var _table$initialState$c;\n      table.setColumnVisibility(defaultState ? {} : (_table$initialState$c = table.initialState.columnVisibility) != null ? _table$initialState$c : {});\n    };\n    table.toggleAllColumnsVisible = value => {\n      var _value;\n      value = (_value = value) != null ? _value : !table.getIsAllColumnsVisible();\n      table.setColumnVisibility(table.getAllLeafColumns().reduce((obj, column) => ({\n        ...obj,\n        [column.id]: !value ? !(column.getCanHide != null && column.getCanHide()) : value\n      }), {}));\n    };\n    table.getIsAllColumnsVisible = () => !table.getAllLeafColumns().some(column => !(column.getIsVisible != null && column.getIsVisible()));\n    table.getIsSomeColumnsVisible = () => table.getAllLeafColumns().some(column => column.getIsVisible == null ? void 0 : column.getIsVisible());\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return e => {\n        var _target;\n        table.toggleAllColumnsVisible((_target = e.target) == null ? void 0 : _target.checked);\n      };\n    };\n  }\n};\nfunction _getVisibleLeafColumns(table, position) {\n  return !position ? table.getVisibleLeafColumns() : position === 'center' ? table.getCenterVisibleLeafColumns() : position === 'left' ? table.getLeftVisibleLeafColumns() : table.getRightVisibleLeafColumns();\n}\n\n//\n\nconst GlobalFaceting = {\n  createTable: table => {\n    table._getGlobalFacetedRowModel = table.options.getFacetedRowModel && table.options.getFacetedRowModel(table, '__global__');\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return table._getGlobalFacetedRowModel();\n    };\n    table._getGlobalFacetedUniqueValues = table.options.getFacetedUniqueValues && table.options.getFacetedUniqueValues(table, '__global__');\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map();\n      }\n      return table._getGlobalFacetedUniqueValues();\n    };\n    table._getGlobalFacetedMinMaxValues = table.options.getFacetedMinMaxValues && table.options.getFacetedMinMaxValues(table, '__global__');\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return;\n      }\n      return table._getGlobalFacetedMinMaxValues();\n    };\n  }\n};\n\n//\n\nconst GlobalFiltering = {\n  getInitialState: state => {\n    return {\n      globalFilter: undefined,\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        var _table$getCoreRowMode;\n        const value = (_table$getCoreRowMode = table.getCoreRowModel().flatRows[0]) == null || (_table$getCoreRowMode = _table$getCoreRowMode._getAllCellsByColumnId()[column.id]) == null ? void 0 : _table$getCoreRowMode.getValue();\n        return typeof value === 'string' || typeof value === 'number';\n      }\n    };\n  },\n  createColumn: (column, table) => {\n    column.getCanGlobalFilter = () => {\n      var _column$columnDef$ena, _table$options$enable, _table$options$enable2, _table$options$getCol;\n      return ((_column$columnDef$ena = column.columnDef.enableGlobalFilter) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableGlobalFilter) != null ? _table$options$enable : true) && ((_table$options$enable2 = table.options.enableFilters) != null ? _table$options$enable2 : true) && ((_table$options$getCol = table.options.getColumnCanGlobalFilter == null ? void 0 : table.options.getColumnCanGlobalFilter(column)) != null ? _table$options$getCol : true) && !!column.accessorFn;\n    };\n  },\n  createTable: table => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString;\n    };\n    table.getGlobalFilterFn = () => {\n      var _table$options$filter, _table$options$filter2;\n      const {\n        globalFilterFn: globalFilterFn\n      } = table.options;\n      return isFunction(globalFilterFn) ? globalFilterFn : globalFilterFn === 'auto' ? table.getGlobalAutoFilterFn() : (_table$options$filter = (_table$options$filter2 = table.options.filterFns) == null ? void 0 : _table$options$filter2[globalFilterFn]) != null ? _table$options$filter : filterFns[globalFilterFn];\n    };\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange == null || table.options.onGlobalFilterChange(updater);\n    };\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(defaultState ? undefined : table.initialState.globalFilter);\n    };\n  }\n};\n\n//\n\nconst RowExpanding = {\n  getInitialState: state => {\n    return {\n      expanded: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true\n    };\n  },\n  createTable: table => {\n    let registered = false;\n    let queued = false;\n    table._autoResetExpanded = () => {\n      var _ref, _table$options$autoRe;\n      if (!registered) {\n        table._queue(() => {\n          registered = true;\n        });\n        return;\n      }\n      if ((_ref = (_table$options$autoRe = table.options.autoResetAll) != null ? _table$options$autoRe : table.options.autoResetExpanded) != null ? _ref : !table.options.manualExpanding) {\n        if (queued) return;\n        queued = true;\n        table._queue(() => {\n          table.resetExpanded();\n          queued = false;\n        });\n      }\n    };\n    table.setExpanded = updater => table.options.onExpandedChange == null ? void 0 : table.options.onExpandedChange(updater);\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded != null ? expanded : !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true);\n      } else {\n        table.setExpanded({});\n      }\n    };\n    table.resetExpanded = defaultState => {\n      var _table$initialState$e, _table$initialState;\n      table.setExpanded(defaultState ? {} : (_table$initialState$e = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.expanded) != null ? _table$initialState$e : {});\n    };\n    table.getCanSomeRowsExpand = () => {\n      return table.getPrePaginationRowModel().flatRows.some(row => row.getCanExpand());\n    };\n    table.getToggleAllRowsExpandedHandler = () => {\n      return e => {\n        e.persist == null || e.persist();\n        table.toggleAllRowsExpanded();\n      };\n    };\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded;\n      return expanded === true || Object.values(expanded).some(Boolean);\n    };\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded;\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true;\n      }\n      if (!Object.keys(expanded).length) {\n        return false;\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false;\n      }\n\n      // They must all be expanded :shrug:\n      return true;\n    };\n    table.getExpandedDepth = () => {\n      let maxDepth = 0;\n      const rowIds = table.getState().expanded === true ? Object.keys(table.getRowModel().rowsById) : Object.keys(table.getState().expanded);\n      rowIds.forEach(id => {\n        const splitId = id.split('.');\n        maxDepth = Math.max(maxDepth, splitId.length);\n      });\n      return maxDepth;\n    };\n    table.getPreExpandedRowModel = () => table.getSortedRowModel();\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table);\n      }\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel();\n      }\n      return table._getExpandedRowModel();\n    };\n  },\n  createRow: (row, table) => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        var _expanded;\n        const exists = old === true ? true : !!(old != null && old[row.id]);\n        let oldExpanded = {};\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true;\n          });\n        } else {\n          oldExpanded = old;\n        }\n        expanded = (_expanded = expanded) != null ? _expanded : !exists;\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true\n          };\n        }\n        if (exists && !expanded) {\n          const {\n            [row.id]: _,\n            ...rest\n          } = oldExpanded;\n          return rest;\n        }\n        return old;\n      });\n    };\n    row.getIsExpanded = () => {\n      var _table$options$getIsR;\n      const expanded = table.getState().expanded;\n      return !!((_table$options$getIsR = table.options.getIsRowExpanded == null ? void 0 : table.options.getIsRowExpanded(row)) != null ? _table$options$getIsR : expanded === true || (expanded == null ? void 0 : expanded[row.id]));\n    };\n    row.getCanExpand = () => {\n      var _table$options$getRow, _table$options$enable, _row$subRows;\n      return (_table$options$getRow = table.options.getRowCanExpand == null ? void 0 : table.options.getRowCanExpand(row)) != null ? _table$options$getRow : ((_table$options$enable = table.options.enableExpanding) != null ? _table$options$enable : true) && !!((_row$subRows = row.subRows) != null && _row$subRows.length);\n    };\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true;\n      let currentRow = row;\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true);\n        isFullyExpanded = currentRow.getIsExpanded();\n      }\n      return isFullyExpanded;\n    };\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand();\n      return () => {\n        if (!canExpand) return;\n        row.toggleExpanded();\n      };\n    };\n  }\n};\n\n//\n\nconst defaultPageIndex = 0;\nconst defaultPageSize = 10;\nconst getDefaultPaginationState = () => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize\n});\nconst RowPagination = {\n  getInitialState: state => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...(state == null ? void 0 : state.pagination)\n      }\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table)\n    };\n  },\n  createTable: table => {\n    let registered = false;\n    let queued = false;\n    table._autoResetPageIndex = () => {\n      var _ref, _table$options$autoRe;\n      if (!registered) {\n        table._queue(() => {\n          registered = true;\n        });\n        return;\n      }\n      if ((_ref = (_table$options$autoRe = table.options.autoResetAll) != null ? _table$options$autoRe : table.options.autoResetPageIndex) != null ? _ref : !table.options.manualPagination) {\n        if (queued) return;\n        queued = true;\n        table._queue(() => {\n          table.resetPageIndex();\n          queued = false;\n        });\n      }\n    };\n    table.setPagination = updater => {\n      const safeUpdater = old => {\n        let newState = functionalUpdate(updater, old);\n        return newState;\n      };\n      return table.options.onPaginationChange == null ? void 0 : table.options.onPaginationChange(safeUpdater);\n    };\n    table.resetPagination = defaultState => {\n      var _table$initialState$p;\n      table.setPagination(defaultState ? getDefaultPaginationState() : (_table$initialState$p = table.initialState.pagination) != null ? _table$initialState$p : getDefaultPaginationState());\n    };\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex);\n        const maxPageIndex = typeof table.options.pageCount === 'undefined' || table.options.pageCount === -1 ? Number.MAX_SAFE_INTEGER : table.options.pageCount - 1;\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex));\n        return {\n          ...old,\n          pageIndex\n        };\n      });\n    };\n    table.resetPageIndex = defaultState => {\n      var _table$initialState$p2, _table$initialState;\n      table.setPageIndex(defaultState ? defaultPageIndex : (_table$initialState$p2 = (_table$initialState = table.initialState) == null || (_table$initialState = _table$initialState.pagination) == null ? void 0 : _table$initialState.pageIndex) != null ? _table$initialState$p2 : defaultPageIndex);\n    };\n    table.resetPageSize = defaultState => {\n      var _table$initialState$p3, _table$initialState2;\n      table.setPageSize(defaultState ? defaultPageSize : (_table$initialState$p3 = (_table$initialState2 = table.initialState) == null || (_table$initialState2 = _table$initialState2.pagination) == null ? void 0 : _table$initialState2.pageSize) != null ? _table$initialState$p3 : defaultPageSize);\n    };\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize));\n        const topRowIndex = old.pageSize * old.pageIndex;\n        const pageIndex = Math.floor(topRowIndex / pageSize);\n        return {\n          ...old,\n          pageIndex,\n          pageSize\n        };\n      });\n    };\n    //deprecated\n    table.setPageCount = updater => table.setPagination(old => {\n      var _table$options$pageCo;\n      let newPageCount = functionalUpdate(updater, (_table$options$pageCo = table.options.pageCount) != null ? _table$options$pageCo : -1);\n      if (typeof newPageCount === 'number') {\n        newPageCount = Math.max(-1, newPageCount);\n      }\n      return {\n        ...old,\n        pageCount: newPageCount\n      };\n    });\n    table.getPageOptions = memo(() => [table.getPageCount()], pageCount => {\n      let pageOptions = [];\n      if (pageCount && pageCount > 0) {\n        pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i);\n      }\n      return pageOptions;\n    }, getMemoOptions(table.options, 'debugTable', 'getPageOptions'));\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0;\n    table.getCanNextPage = () => {\n      const {\n        pageIndex\n      } = table.getState().pagination;\n      const pageCount = table.getPageCount();\n      if (pageCount === -1) {\n        return true;\n      }\n      if (pageCount === 0) {\n        return false;\n      }\n      return pageIndex < pageCount - 1;\n    };\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1);\n    };\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1;\n      });\n    };\n    table.firstPage = () => {\n      return table.setPageIndex(0);\n    };\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1);\n    };\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel();\n    table.getPaginationRowModel = () => {\n      if (!table._getPaginationRowModel && table.options.getPaginationRowModel) {\n        table._getPaginationRowModel = table.options.getPaginationRowModel(table);\n      }\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel();\n      }\n      return table._getPaginationRowModel();\n    };\n    table.getPageCount = () => {\n      var _table$options$pageCo2;\n      return (_table$options$pageCo2 = table.options.pageCount) != null ? _table$options$pageCo2 : Math.ceil(table.getRowCount() / table.getState().pagination.pageSize);\n    };\n    table.getRowCount = () => {\n      var _table$options$rowCou;\n      return (_table$options$rowCou = table.options.rowCount) != null ? _table$options$rowCou : table.getPrePaginationRowModel().rows.length;\n    };\n  }\n};\n\n//\n\nconst getDefaultRowPinningState = () => ({\n  top: [],\n  bottom: []\n});\nconst RowPinning = {\n  getInitialState: state => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table)\n    };\n  },\n  createRow: (row, table) => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows ? row.getLeafRows().map(_ref => {\n        let {\n          id\n        } = _ref;\n        return id;\n      }) : [];\n      const parentRowIds = includeParentRows ? row.getParentRows().map(_ref2 => {\n        let {\n          id\n        } = _ref2;\n        return id;\n      }) : [];\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds]);\n      table.setRowPinning(old => {\n        var _old$top3, _old$bottom3;\n        if (position === 'bottom') {\n          var _old$top, _old$bottom;\n          return {\n            top: ((_old$top = old == null ? void 0 : old.top) != null ? _old$top : []).filter(d => !(rowIds != null && rowIds.has(d))),\n            bottom: [...((_old$bottom = old == null ? void 0 : old.bottom) != null ? _old$bottom : []).filter(d => !(rowIds != null && rowIds.has(d))), ...Array.from(rowIds)]\n          };\n        }\n        if (position === 'top') {\n          var _old$top2, _old$bottom2;\n          return {\n            top: [...((_old$top2 = old == null ? void 0 : old.top) != null ? _old$top2 : []).filter(d => !(rowIds != null && rowIds.has(d))), ...Array.from(rowIds)],\n            bottom: ((_old$bottom2 = old == null ? void 0 : old.bottom) != null ? _old$bottom2 : []).filter(d => !(rowIds != null && rowIds.has(d)))\n          };\n        }\n        return {\n          top: ((_old$top3 = old == null ? void 0 : old.top) != null ? _old$top3 : []).filter(d => !(rowIds != null && rowIds.has(d))),\n          bottom: ((_old$bottom3 = old == null ? void 0 : old.bottom) != null ? _old$bottom3 : []).filter(d => !(rowIds != null && rowIds.has(d)))\n        };\n      });\n    };\n    row.getCanPin = () => {\n      var _ref3;\n      const {\n        enableRowPinning,\n        enablePinning\n      } = table.options;\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row);\n      }\n      return (_ref3 = enableRowPinning != null ? enableRowPinning : enablePinning) != null ? _ref3 : true;\n    };\n    row.getIsPinned = () => {\n      const rowIds = [row.id];\n      const {\n        top,\n        bottom\n      } = table.getState().rowPinning;\n      const isTop = rowIds.some(d => top == null ? void 0 : top.includes(d));\n      const isBottom = rowIds.some(d => bottom == null ? void 0 : bottom.includes(d));\n      return isTop ? 'top' : isBottom ? 'bottom' : false;\n    };\n    row.getPinnedIndex = () => {\n      var _ref4, _visiblePinnedRowIds$;\n      const position = row.getIsPinned();\n      if (!position) return -1;\n      const visiblePinnedRowIds = (_ref4 = position === 'top' ? table.getTopRows() : table.getBottomRows()) == null ? void 0 : _ref4.map(_ref5 => {\n        let {\n          id\n        } = _ref5;\n        return id;\n      });\n      return (_visiblePinnedRowIds$ = visiblePinnedRowIds == null ? void 0 : visiblePinnedRowIds.indexOf(row.id)) != null ? _visiblePinnedRowIds$ : -1;\n    };\n  },\n  createTable: table => {\n    table.setRowPinning = updater => table.options.onRowPinningChange == null ? void 0 : table.options.onRowPinningChange(updater);\n    table.resetRowPinning = defaultState => {\n      var _table$initialState$r, _table$initialState;\n      return table.setRowPinning(defaultState ? getDefaultRowPinningState() : (_table$initialState$r = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.rowPinning) != null ? _table$initialState$r : getDefaultRowPinningState());\n    };\n    table.getIsSomeRowsPinned = position => {\n      var _pinningState$positio;\n      const pinningState = table.getState().rowPinning;\n      if (!position) {\n        var _pinningState$top, _pinningState$bottom;\n        return Boolean(((_pinningState$top = pinningState.top) == null ? void 0 : _pinningState$top.length) || ((_pinningState$bottom = pinningState.bottom) == null ? void 0 : _pinningState$bottom.length));\n      }\n      return Boolean((_pinningState$positio = pinningState[position]) == null ? void 0 : _pinningState$positio.length);\n    };\n    table._getPinnedRows = (visibleRows, pinnedRowIds, position) => {\n      var _table$options$keepPi;\n      const rows = ((_table$options$keepPi = table.options.keepPinnedRows) != null ? _table$options$keepPi : true) ?\n      //get all rows that are pinned even if they would not be otherwise visible\n      //account for expanded parent rows, but not pagination or filtering\n      (pinnedRowIds != null ? pinnedRowIds : []).map(rowId => {\n        const row = table.getRow(rowId, true);\n        return row.getIsAllParentsExpanded() ? row : null;\n      }) :\n      //else get only visible rows that are pinned\n      (pinnedRowIds != null ? pinnedRowIds : []).map(rowId => visibleRows.find(row => row.id === rowId));\n      return rows.filter(Boolean).map(d => ({\n        ...d,\n        position\n      }));\n    };\n    table.getTopRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.top], (allRows, topPinnedRowIds) => table._getPinnedRows(allRows, topPinnedRowIds, 'top'), getMemoOptions(table.options, 'debugRows', 'getTopRows'));\n    table.getBottomRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.bottom], (allRows, bottomPinnedRowIds) => table._getPinnedRows(allRows, bottomPinnedRowIds, 'bottom'), getMemoOptions(table.options, 'debugRows', 'getBottomRows'));\n    table.getCenterRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.top, table.getState().rowPinning.bottom], (allRows, top, bottom) => {\n      const topAndBottom = new Set([...(top != null ? top : []), ...(bottom != null ? bottom : [])]);\n      return allRows.filter(d => !topAndBottom.has(d.id));\n    }, getMemoOptions(table.options, 'debugRows', 'getCenterRows'));\n  }\n};\n\n//\n\nconst RowSelection = {\n  getInitialState: state => {\n    return {\n      rowSelection: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    };\n  },\n  createTable: table => {\n    table.setRowSelection = updater => table.options.onRowSelectionChange == null ? void 0 : table.options.onRowSelectionChange(updater);\n    table.resetRowSelection = defaultState => {\n      var _table$initialState$r;\n      return table.setRowSelection(defaultState ? {} : (_table$initialState$r = table.initialState.rowSelection) != null ? _table$initialState$r : {});\n    };\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected();\n        const rowSelection = {\n          ...old\n        };\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows;\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return;\n            }\n            rowSelection[row.id] = true;\n          });\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id];\n          });\n        }\n        return rowSelection;\n      });\n    };\n    table.toggleAllPageRowsSelected = value => table.setRowSelection(old => {\n      const resolvedValue = typeof value !== 'undefined' ? value : !table.getIsAllPageRowsSelected();\n      const rowSelection = {\n        ...old\n      };\n      table.getRowModel().rows.forEach(row => {\n        mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table);\n      });\n      return rowSelection;\n    });\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel();\n    table.getSelectedRowModel = memo(() => [table.getState().rowSelection, table.getCoreRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel'));\n    table.getFilteredSelectedRowModel = memo(() => [table.getState().rowSelection, table.getFilteredRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel'));\n    table.getGroupedSelectedRowModel = memo(() => [table.getState().rowSelection, table.getSortedRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel'));\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows;\n      const {\n        rowSelection\n      } = table.getState();\n      let isAllRowsSelected = Boolean(preGroupedFlatRows.length && Object.keys(rowSelection).length);\n      if (isAllRowsSelected) {\n        if (preGroupedFlatRows.some(row => row.getCanSelect() && !rowSelection[row.id])) {\n          isAllRowsSelected = false;\n        }\n      }\n      return isAllRowsSelected;\n    };\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows.filter(row => row.getCanSelect());\n      const {\n        rowSelection\n      } = table.getState();\n      let isAllPageRowsSelected = !!paginationFlatRows.length;\n      if (isAllPageRowsSelected && paginationFlatRows.some(row => !rowSelection[row.id])) {\n        isAllPageRowsSelected = false;\n      }\n      return isAllPageRowsSelected;\n    };\n    table.getIsSomeRowsSelected = () => {\n      var _table$getState$rowSe;\n      const totalSelected = Object.keys((_table$getState$rowSe = table.getState().rowSelection) != null ? _table$getState$rowSe : {}).length;\n      return totalSelected > 0 && totalSelected < table.getFilteredRowModel().flatRows.length;\n    };\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows;\n      return table.getIsAllPageRowsSelected() ? false : paginationFlatRows.filter(row => row.getCanSelect()).some(d => d.getIsSelected() || d.getIsSomeSelected());\n    };\n    table.getToggleAllRowsSelectedHandler = () => {\n      return e => {\n        table.toggleAllRowsSelected(e.target.checked);\n      };\n    };\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return e => {\n        table.toggleAllPageRowsSelected(e.target.checked);\n      };\n    };\n  },\n  createRow: (row, table) => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected();\n      table.setRowSelection(old => {\n        var _opts$selectChildren;\n        value = typeof value !== 'undefined' ? value : !isSelected;\n        if (row.getCanSelect() && isSelected === value) {\n          return old;\n        }\n        const selectedRowIds = {\n          ...old\n        };\n        mutateRowIsSelected(selectedRowIds, row.id, value, (_opts$selectChildren = opts == null ? void 0 : opts.selectChildren) != null ? _opts$selectChildren : true, table);\n        return selectedRowIds;\n      });\n    };\n    row.getIsSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isRowSelected(row, rowSelection);\n    };\n    row.getIsSomeSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isSubRowSelected(row, rowSelection) === 'some';\n    };\n    row.getIsAllSubRowsSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isSubRowSelected(row, rowSelection) === 'all';\n    };\n    row.getCanSelect = () => {\n      var _table$options$enable;\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row);\n      }\n      return (_table$options$enable = table.options.enableRowSelection) != null ? _table$options$enable : true;\n    };\n    row.getCanSelectSubRows = () => {\n      var _table$options$enable2;\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row);\n      }\n      return (_table$options$enable2 = table.options.enableSubRowSelection) != null ? _table$options$enable2 : true;\n    };\n    row.getCanMultiSelect = () => {\n      var _table$options$enable3;\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row);\n      }\n      return (_table$options$enable3 = table.options.enableMultiRowSelection) != null ? _table$options$enable3 : true;\n    };\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect();\n      return e => {\n        var _target;\n        if (!canSelect) return;\n        row.toggleSelected((_target = e.target) == null ? void 0 : _target.checked);\n      };\n    };\n  }\n};\nconst mutateRowIsSelected = (selectedRowIds, id, value, includeChildren, table) => {\n  var _row$subRows;\n  const row = table.getRow(id, true);\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key]);\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true;\n    }\n  } else {\n    delete selectedRowIds[id];\n  }\n  // }\n\n  if (includeChildren && (_row$subRows = row.subRows) != null && _row$subRows.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row => mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table));\n  }\n};\nfunction selectRowsFn(table, rowModel) {\n  const rowSelection = table.getState().rowSelection;\n  const newSelectedFlatRows = [];\n  const newSelectedRowsById = {};\n\n  // Filters top level and nested rows\n  const recurseRows = function (rows, depth) {\n    return rows.map(row => {\n      var _row$subRows2;\n      const isSelected = isRowSelected(row, rowSelection);\n      if (isSelected) {\n        newSelectedFlatRows.push(row);\n        newSelectedRowsById[row.id] = row;\n      }\n      if ((_row$subRows2 = row.subRows) != null && _row$subRows2.length) {\n        row = {\n          ...row,\n          subRows: recurseRows(row.subRows)\n        };\n      }\n      if (isSelected) {\n        return row;\n      }\n    }).filter(Boolean);\n  };\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById\n  };\n}\nfunction isRowSelected(row, selection) {\n  var _selection$row$id;\n  return (_selection$row$id = selection[row.id]) != null ? _selection$row$id : false;\n}\nfunction isSubRowSelected(row, selection, table) {\n  var _row$subRows3;\n  if (!((_row$subRows3 = row.subRows) != null && _row$subRows3.length)) return false;\n  let allChildrenSelected = true;\n  let someSelected = false;\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return;\n    }\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true;\n      } else {\n        allChildrenSelected = false;\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection);\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true;\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true;\n        allChildrenSelected = false;\n      } else {\n        allChildrenSelected = false;\n      }\n    }\n  });\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false;\n}\n\nconst reSplitAlphaNumeric = /([0-9]+)/gm;\nconst alphanumeric = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(toString(rowA.getValue(columnId)).toLowerCase(), toString(rowB.getValue(columnId)).toLowerCase());\n};\nconst alphanumericCaseSensitive = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(toString(rowA.getValue(columnId)), toString(rowB.getValue(columnId)));\n};\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text = (rowA, rowB, columnId) => {\n  return compareBasic(toString(rowA.getValue(columnId)).toLowerCase(), toString(rowB.getValue(columnId)).toLowerCase());\n};\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive = (rowA, rowB, columnId) => {\n  return compareBasic(toString(rowA.getValue(columnId)), toString(rowB.getValue(columnId)));\n};\nconst datetime = (rowA, rowB, columnId) => {\n  const a = rowA.getValue(columnId);\n  const b = rowB.getValue(columnId);\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0;\n};\nconst basic = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId));\n};\n\n// Utils\n\nfunction compareBasic(a, b) {\n  return a === b ? 0 : a > b ? 1 : -1;\n}\nfunction toString(a) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return '';\n    }\n    return String(a);\n  }\n  if (typeof a === 'string') {\n    return a;\n  }\n  return '';\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr, bStr) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean);\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean);\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift();\n    const bb = b.shift();\n    const an = parseInt(aa, 10);\n    const bn = parseInt(bb, 10);\n    const combo = [an, bn].sort();\n\n    // Both are string\n    if (isNaN(combo[0])) {\n      if (aa > bb) {\n        return 1;\n      }\n      if (bb > aa) {\n        return -1;\n      }\n      continue;\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1])) {\n      return isNaN(an) ? -1 : 1;\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1;\n    }\n    if (bn > an) {\n      return -1;\n    }\n  }\n  return a.length - b.length;\n}\n\n// Exports\n\nconst sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic\n};\n\n//\n\nconst RowSorting = {\n  getInitialState: state => {\n    return {\n      sorting: [],\n      ...state\n    };\n  },\n  getDefaultColumnDef: () => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: e => {\n        return e.shiftKey;\n      }\n    };\n  },\n  createColumn: (column, table) => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10);\n      let isString = false;\n      for (const row of firstRows) {\n        const value = row == null ? void 0 : row.getValue(column.id);\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime;\n        }\n        if (typeof value === 'string') {\n          isString = true;\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric;\n          }\n        }\n      }\n      if (isString) {\n        return sortingFns.text;\n      }\n      return sortingFns.basic;\n    };\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'string') {\n        return 'asc';\n      }\n      return 'desc';\n    };\n    column.getSortingFn = () => {\n      var _table$options$sortin, _table$options$sortin2;\n      if (!column) {\n        throw new Error();\n      }\n      return isFunction(column.columnDef.sortingFn) ? column.columnDef.sortingFn : column.columnDef.sortingFn === 'auto' ? column.getAutoSortingFn() : (_table$options$sortin = (_table$options$sortin2 = table.options.sortingFns) == null ? void 0 : _table$options$sortin2[column.columnDef.sortingFn]) != null ? _table$options$sortin : sortingFns[column.columnDef.sortingFn];\n    };\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder();\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null;\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old == null ? void 0 : old.find(d => d.id === column.id);\n        const existingIndex = old == null ? void 0 : old.findIndex(d => d.id === column.id);\n        let newSorting = [];\n\n        // What should we do with this sort action?\n        let sortAction;\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc';\n\n        // Multi-mode\n        if (old != null && old.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle';\n          } else {\n            sortAction = 'add';\n          }\n        } else {\n          // Normal mode\n          if (old != null && old.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace';\n          } else if (existingSorting) {\n            sortAction = 'toggle';\n          } else {\n            sortAction = 'replace';\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove';\n            }\n          }\n        }\n        if (sortAction === 'add') {\n          var _table$options$maxMul;\n          newSorting = [...old, {\n            id: column.id,\n            desc: nextDesc\n          }];\n          // Take latest n columns\n          newSorting.splice(0, newSorting.length - ((_table$options$maxMul = table.options.maxMultiSortColCount) != null ? _table$options$maxMul : Number.MAX_SAFE_INTEGER));\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc\n              };\n            }\n            return d;\n          });\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id);\n        } else {\n          newSorting = [{\n            id: column.id,\n            desc: nextDesc\n          }];\n        }\n        return newSorting;\n      });\n    };\n    column.getFirstSortDir = () => {\n      var _ref, _column$columnDef$sor;\n      const sortDescFirst = (_ref = (_column$columnDef$sor = column.columnDef.sortDescFirst) != null ? _column$columnDef$sor : table.options.sortDescFirst) != null ? _ref : column.getAutoSortDir() === 'desc';\n      return sortDescFirst ? 'desc' : 'asc';\n    };\n    column.getNextSortingOrder = multi => {\n      var _table$options$enable, _table$options$enable2;\n      const firstSortDirection = column.getFirstSortDir();\n      const isSorted = column.getIsSorted();\n      if (!isSorted) {\n        return firstSortDirection;\n      }\n      if (isSorted !== firstSortDirection && ((_table$options$enable = table.options.enableSortingRemoval) != null ? _table$options$enable : true) && (\n      // If enableSortRemove, enable in general\n      multi ? (_table$options$enable2 = table.options.enableMultiRemove) != null ? _table$options$enable2 : true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false;\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc';\n    };\n    column.getCanSort = () => {\n      var _column$columnDef$ena, _table$options$enable3;\n      return ((_column$columnDef$ena = column.columnDef.enableSorting) != null ? _column$columnDef$ena : true) && ((_table$options$enable3 = table.options.enableSorting) != null ? _table$options$enable3 : true) && !!column.accessorFn;\n    };\n    column.getCanMultiSort = () => {\n      var _ref2, _column$columnDef$ena2;\n      return (_ref2 = (_column$columnDef$ena2 = column.columnDef.enableMultiSort) != null ? _column$columnDef$ena2 : table.options.enableMultiSort) != null ? _ref2 : !!column.accessorFn;\n    };\n    column.getIsSorted = () => {\n      var _table$getState$sorti;\n      const columnSort = (_table$getState$sorti = table.getState().sorting) == null ? void 0 : _table$getState$sorti.find(d => d.id === column.id);\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc';\n    };\n    column.getSortIndex = () => {\n      var _table$getState$sorti2, _table$getState$sorti3;\n      return (_table$getState$sorti2 = (_table$getState$sorti3 = table.getState().sorting) == null ? void 0 : _table$getState$sorti3.findIndex(d => d.id === column.id)) != null ? _table$getState$sorti2 : -1;\n    };\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old => old != null && old.length ? old.filter(d => d.id !== column.id) : []);\n    };\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort();\n      return e => {\n        if (!canSort) return;\n        e.persist == null || e.persist();\n        column.toggleSorting == null || column.toggleSorting(undefined, column.getCanMultiSort() ? table.options.isMultiSortEvent == null ? void 0 : table.options.isMultiSortEvent(e) : false);\n      };\n    };\n  },\n  createTable: table => {\n    table.setSorting = updater => table.options.onSortingChange == null ? void 0 : table.options.onSortingChange(updater);\n    table.resetSorting = defaultState => {\n      var _table$initialState$s, _table$initialState;\n      table.setSorting(defaultState ? [] : (_table$initialState$s = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.sorting) != null ? _table$initialState$s : []);\n    };\n    table.getPreSortedRowModel = () => table.getGroupedRowModel();\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table);\n      }\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel();\n      }\n      return table._getSortedRowModel();\n    };\n  }\n};\n\nconst builtInFeatures = [Headers, ColumnVisibility, ColumnOrdering, ColumnPinning, ColumnFaceting, ColumnFiltering, GlobalFaceting,\n//depends on ColumnFaceting\nGlobalFiltering,\n//depends on ColumnFiltering\nRowSorting, ColumnGrouping,\n//depends on RowSorting\nRowExpanding, RowPagination, RowPinning, RowSelection, ColumnSizing];\n\n//\n\nfunction createTable(options) {\n  var _options$_features, _options$initialState;\n  if ( true && (options.debugAll || options.debugTable)) {\n    console.info('Creating Table Instance...');\n  }\n  const _features = [...builtInFeatures, ...((_options$_features = options._features) != null ? _options$_features : [])];\n  let table = {\n    _features\n  };\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions == null ? void 0 : feature.getDefaultOptions(table));\n  }, {});\n  const mergeOptions = options => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options);\n    }\n    return {\n      ...defaultOptions,\n      ...options\n    };\n  };\n  const coreInitialState = {};\n  let initialState = {\n    ...coreInitialState,\n    ...((_options$initialState = options.initialState) != null ? _options$initialState : {})\n  };\n  table._features.forEach(feature => {\n    var _feature$getInitialSt;\n    initialState = (_feature$getInitialSt = feature.getInitialState == null ? void 0 : feature.getInitialState(initialState)) != null ? _feature$getInitialSt : initialState;\n  });\n  const queued = [];\n  let queuedTimeout = false;\n  const coreInstance = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb);\n      if (!queuedTimeout) {\n        queuedTimeout = true;\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve().then(() => {\n          while (queued.length) {\n            queued.shift()();\n          }\n          queuedTimeout = false;\n        }).catch(error => setTimeout(() => {\n          throw error;\n        }));\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState);\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options);\n      table.options = mergeOptions(newOptions);\n    },\n    getState: () => {\n      return table.options.state;\n    },\n    setState: updater => {\n      table.options.onStateChange == null || table.options.onStateChange(updater);\n    },\n    _getRowId: (row, index, parent) => {\n      var _table$options$getRow;\n      return (_table$options$getRow = table.options.getRowId == null ? void 0 : table.options.getRowId(row, index, parent)) != null ? _table$options$getRow : `${parent ? [parent.id, index].join('.') : index}`;\n    },\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table);\n      }\n      return table._getCoreRowModel();\n    },\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel();\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id, searchAll) => {\n      let row = (searchAll ? table.getPrePaginationRowModel() : table.getRowModel()).rowsById[id];\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id];\n        if (!row) {\n          if (true) {\n            throw new Error(`getRow could not find row with ID: ${id}`);\n          }\n          throw new Error();\n        }\n      }\n      return row;\n    },\n    _getDefaultColumnDef: memo(() => [table.options.defaultColumn], defaultColumn => {\n      var _defaultColumn;\n      defaultColumn = (_defaultColumn = defaultColumn) != null ? _defaultColumn : {};\n      return {\n        header: props => {\n          const resolvedColumnDef = props.header.column.columnDef;\n          if (resolvedColumnDef.accessorKey) {\n            return resolvedColumnDef.accessorKey;\n          }\n          if (resolvedColumnDef.accessorFn) {\n            return resolvedColumnDef.id;\n          }\n          return null;\n        },\n        // footer: props => props.header.column.id,\n        cell: props => {\n          var _props$renderValue$to, _props$renderValue;\n          return (_props$renderValue$to = (_props$renderValue = props.renderValue()) == null || _props$renderValue.toString == null ? void 0 : _props$renderValue.toString()) != null ? _props$renderValue$to : null;\n        },\n        ...table._features.reduce((obj, feature) => {\n          return Object.assign(obj, feature.getDefaultColumnDef == null ? void 0 : feature.getDefaultColumnDef());\n        }, {}),\n        ...defaultColumn\n      };\n    }, getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')),\n    _getColumnDefs: () => table.options.columns,\n    getAllColumns: memo(() => [table._getColumnDefs()], columnDefs => {\n      const recurseColumns = function (columnDefs, parent, depth) {\n        if (depth === void 0) {\n          depth = 0;\n        }\n        return columnDefs.map(columnDef => {\n          const column = createColumn(table, columnDef, depth, parent);\n          const groupingColumnDef = columnDef;\n          column.columns = groupingColumnDef.columns ? recurseColumns(groupingColumnDef.columns, column, depth + 1) : [];\n          return column;\n        });\n      };\n      return recurseColumns(columnDefs);\n    }, getMemoOptions(options, 'debugColumns', 'getAllColumns')),\n    getAllFlatColumns: memo(() => [table.getAllColumns()], allColumns => {\n      return allColumns.flatMap(column => {\n        return column.getFlatColumns();\n      });\n    }, getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')),\n    _getAllFlatColumnsById: memo(() => [table.getAllFlatColumns()], flatColumns => {\n      return flatColumns.reduce((acc, column) => {\n        acc[column.id] = column;\n        return acc;\n      }, {});\n    }, getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')),\n    getAllLeafColumns: memo(() => [table.getAllColumns(), table._getOrderColumnsFn()], (allColumns, orderColumns) => {\n      let leafColumns = allColumns.flatMap(column => column.getLeafColumns());\n      return orderColumns(leafColumns);\n    }, getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')),\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId];\n      if ( true && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`);\n      }\n      return column;\n    }\n  };\n  Object.assign(table, coreInstance);\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index];\n    feature == null || feature.createTable == null || feature.createTable(table);\n  }\n  return table;\n}\n\nfunction getCoreRowModel() {\n  return table => memo(() => [table.options.data], data => {\n    const rowModel = {\n      rows: [],\n      flatRows: [],\n      rowsById: {}\n    };\n    const accessRows = function (originalRows, depth, parentRow) {\n      if (depth === void 0) {\n        depth = 0;\n      }\n      const rows = [];\n      for (let i = 0; i < originalRows.length; i++) {\n        // This could be an expensive check at scale, so we should move it somewhere else, but where?\n        // if (!id) {\n        //   if (process.env.NODE_ENV !== 'production') {\n        //     throw new Error(`getRowId expected an ID, but got ${id}`)\n        //   }\n        // }\n\n        // Make the row\n        const row = createRow(table, table._getRowId(originalRows[i], i, parentRow), originalRows[i], i, depth, undefined, parentRow == null ? void 0 : parentRow.id);\n\n        // Keep track of every row in a flat array\n        rowModel.flatRows.push(row);\n        // Also keep track of every row by its ID\n        rowModel.rowsById[row.id] = row;\n        // Push table row into parent\n        rows.push(row);\n\n        // Get the original subrows\n        if (table.options.getSubRows) {\n          var _row$originalSubRows;\n          row.originalSubRows = table.options.getSubRows(originalRows[i], i);\n\n          // Then recursively access them\n          if ((_row$originalSubRows = row.originalSubRows) != null && _row$originalSubRows.length) {\n            row.subRows = accessRows(row.originalSubRows, depth + 1, row);\n          }\n        }\n      }\n      return rows;\n    };\n    rowModel.rows = accessRows(data);\n    return rowModel;\n  }, getMemoOptions(table.options, 'debugTable', 'getRowModel', () => table._autoResetPageIndex()));\n}\n\nfunction getExpandedRowModel() {\n  return table => memo(() => [table.getState().expanded, table.getPreExpandedRowModel(), table.options.paginateExpandedRows], (expanded, rowModel, paginateExpandedRows) => {\n    if (!rowModel.rows.length || expanded !== true && !Object.keys(expanded != null ? expanded : {}).length) {\n      return rowModel;\n    }\n    if (!paginateExpandedRows) {\n      // Only expand rows at this point if they are being paginated\n      return rowModel;\n    }\n    return expandRows(rowModel);\n  }, getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel'));\n}\nfunction expandRows(rowModel) {\n  const expandedRows = [];\n  const handleRow = row => {\n    var _row$subRows;\n    expandedRows.push(row);\n    if ((_row$subRows = row.subRows) != null && _row$subRows.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow);\n    }\n  };\n  rowModel.rows.forEach(handleRow);\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById\n  };\n}\n\nfunction getFacetedMinMaxValues() {\n  return (table, columnId) => memo(() => {\n    var _table$getColumn;\n    return [(_table$getColumn = table.getColumn(columnId)) == null ? void 0 : _table$getColumn.getFacetedRowModel()];\n  }, facetedRowModel => {\n    if (!facetedRowModel) return undefined;\n    const uniqueValues = facetedRowModel.flatRows.flatMap(flatRow => {\n      var _flatRow$getUniqueVal;\n      return (_flatRow$getUniqueVal = flatRow.getUniqueValues(columnId)) != null ? _flatRow$getUniqueVal : [];\n    }).map(Number).filter(value => !Number.isNaN(value));\n    if (!uniqueValues.length) return;\n    let facetedMinValue = uniqueValues[0];\n    let facetedMaxValue = uniqueValues[uniqueValues.length - 1];\n    for (const value of uniqueValues) {\n      if (value < facetedMinValue) facetedMinValue = value;else if (value > facetedMaxValue) facetedMaxValue = value;\n    }\n    return [facetedMinValue, facetedMaxValue];\n  }, getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues'));\n}\n\nfunction filterRows(rows, filterRowImpl, table) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table);\n  }\n  return filterRowModelFromRoot(rows, filterRowImpl, table);\n}\nfunction filterRowModelFromLeafs(rowsToFilter, filterRow, table) {\n  var _table$options$maxLea;\n  const newFilteredFlatRows = [];\n  const newFilteredRowsById = {};\n  const maxDepth = (_table$options$maxLea = table.options.maxLeafRowFilterDepth) != null ? _table$options$maxLea : 100;\n  const recurseFilterRows = function (rowsToFilter, depth) {\n    if (depth === void 0) {\n      depth = 0;\n    }\n    const rows = [];\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      var _row$subRows;\n      let row = rowsToFilter[i];\n      const newRow = createRow(table, row.id, row.original, row.index, row.depth, undefined, row.parentId);\n      newRow.columnFilters = row.columnFilters;\n      if ((_row$subRows = row.subRows) != null && _row$subRows.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1);\n        row = newRow;\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n          continue;\n        }\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n          continue;\n        }\n      } else {\n        row = newRow;\n        if (filterRow(row)) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n        }\n      }\n    }\n    return rows;\n  };\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById\n  };\n}\nfunction filterRowModelFromRoot(rowsToFilter, filterRow, table) {\n  var _table$options$maxLea2;\n  const newFilteredFlatRows = [];\n  const newFilteredRowsById = {};\n  const maxDepth = (_table$options$maxLea2 = table.options.maxLeafRowFilterDepth) != null ? _table$options$maxLea2 : 100;\n\n  // Filters top level and nested rows\n  const recurseFilterRows = function (rowsToFilter, depth) {\n    if (depth === void 0) {\n      depth = 0;\n    }\n    // Filter from parents downward first\n\n    const rows = [];\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i];\n      const pass = filterRow(row);\n      if (pass) {\n        var _row$subRows2;\n        if ((_row$subRows2 = row.subRows) != null && _row$subRows2.length && depth < maxDepth) {\n          const newRow = createRow(table, row.id, row.original, row.index, row.depth, undefined, row.parentId);\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1);\n          row = newRow;\n        }\n        rows.push(row);\n        newFilteredFlatRows.push(row);\n        newFilteredRowsById[row.id] = row;\n      }\n    }\n    return rows;\n  };\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById\n  };\n}\n\nfunction getFacetedRowModel() {\n  return (table, columnId) => memo(() => [table.getPreFilteredRowModel(), table.getState().columnFilters, table.getState().globalFilter, table.getFilteredRowModel()], (preRowModel, columnFilters, globalFilter) => {\n    if (!preRowModel.rows.length || !(columnFilters != null && columnFilters.length) && !globalFilter) {\n      return preRowModel;\n    }\n    const filterableIds = [...columnFilters.map(d => d.id).filter(d => d !== columnId), globalFilter ? '__global__' : undefined].filter(Boolean);\n    const filterRowsImpl = row => {\n      // Horizontally filter rows through each column\n      for (let i = 0; i < filterableIds.length; i++) {\n        if (row.columnFilters[filterableIds[i]] === false) {\n          return false;\n        }\n      }\n      return true;\n    };\n    return filterRows(preRowModel.rows, filterRowsImpl, table);\n  }, getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel'));\n}\n\nfunction getFacetedUniqueValues() {\n  return (table, columnId) => memo(() => {\n    var _table$getColumn;\n    return [(_table$getColumn = table.getColumn(columnId)) == null ? void 0 : _table$getColumn.getFacetedRowModel()];\n  }, facetedRowModel => {\n    if (!facetedRowModel) return new Map();\n    let facetedUniqueValues = new Map();\n    for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n      const values = facetedRowModel.flatRows[i].getUniqueValues(columnId);\n      for (let j = 0; j < values.length; j++) {\n        const value = values[j];\n        if (facetedUniqueValues.has(value)) {\n          var _facetedUniqueValues$;\n          facetedUniqueValues.set(value, ((_facetedUniqueValues$ = facetedUniqueValues.get(value)) != null ? _facetedUniqueValues$ : 0) + 1);\n        } else {\n          facetedUniqueValues.set(value, 1);\n        }\n      }\n    }\n    return facetedUniqueValues;\n  }, getMemoOptions(table.options, 'debugTable', `getFacetedUniqueValues_${columnId}`));\n}\n\nfunction getFilteredRowModel() {\n  return table => memo(() => [table.getPreFilteredRowModel(), table.getState().columnFilters, table.getState().globalFilter], (rowModel, columnFilters, globalFilter) => {\n    if (!rowModel.rows.length || !(columnFilters != null && columnFilters.length) && !globalFilter) {\n      for (let i = 0; i < rowModel.flatRows.length; i++) {\n        rowModel.flatRows[i].columnFilters = {};\n        rowModel.flatRows[i].columnFiltersMeta = {};\n      }\n      return rowModel;\n    }\n    const resolvedColumnFilters = [];\n    const resolvedGlobalFilters = [];\n    (columnFilters != null ? columnFilters : []).forEach(d => {\n      var _filterFn$resolveFilt;\n      const column = table.getColumn(d.id);\n      if (!column) {\n        return;\n      }\n      const filterFn = column.getFilterFn();\n      if (!filterFn) {\n        if (true) {\n          console.warn(`Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`);\n        }\n        return;\n      }\n      resolvedColumnFilters.push({\n        id: d.id,\n        filterFn,\n        resolvedValue: (_filterFn$resolveFilt = filterFn.resolveFilterValue == null ? void 0 : filterFn.resolveFilterValue(d.value)) != null ? _filterFn$resolveFilt : d.value\n      });\n    });\n    const filterableIds = (columnFilters != null ? columnFilters : []).map(d => d.id);\n    const globalFilterFn = table.getGlobalFilterFn();\n    const globallyFilterableColumns = table.getAllLeafColumns().filter(column => column.getCanGlobalFilter());\n    if (globalFilter && globalFilterFn && globallyFilterableColumns.length) {\n      filterableIds.push('__global__');\n      globallyFilterableColumns.forEach(column => {\n        var _globalFilterFn$resol;\n        resolvedGlobalFilters.push({\n          id: column.id,\n          filterFn: globalFilterFn,\n          resolvedValue: (_globalFilterFn$resol = globalFilterFn.resolveFilterValue == null ? void 0 : globalFilterFn.resolveFilterValue(globalFilter)) != null ? _globalFilterFn$resol : globalFilter\n        });\n      });\n    }\n    let currentColumnFilter;\n    let currentGlobalFilter;\n\n    // Flag the prefiltered row model with each filter state\n    for (let j = 0; j < rowModel.flatRows.length; j++) {\n      const row = rowModel.flatRows[j];\n      row.columnFilters = {};\n      if (resolvedColumnFilters.length) {\n        for (let i = 0; i < resolvedColumnFilters.length; i++) {\n          currentColumnFilter = resolvedColumnFilters[i];\n          const id = currentColumnFilter.id;\n\n          // Tag the row with the column filter state\n          row.columnFilters[id] = currentColumnFilter.filterFn(row, id, currentColumnFilter.resolvedValue, filterMeta => {\n            row.columnFiltersMeta[id] = filterMeta;\n          });\n        }\n      }\n      if (resolvedGlobalFilters.length) {\n        for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n          currentGlobalFilter = resolvedGlobalFilters[i];\n          const id = currentGlobalFilter.id;\n          // Tag the row with the first truthy global filter state\n          if (currentGlobalFilter.filterFn(row, id, currentGlobalFilter.resolvedValue, filterMeta => {\n            row.columnFiltersMeta[id] = filterMeta;\n          })) {\n            row.columnFilters.__global__ = true;\n            break;\n          }\n        }\n        if (row.columnFilters.__global__ !== true) {\n          row.columnFilters.__global__ = false;\n        }\n      }\n    }\n    const filterRowsImpl = row => {\n      // Horizontally filter rows through each column\n      for (let i = 0; i < filterableIds.length; i++) {\n        if (row.columnFilters[filterableIds[i]] === false) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    // Filter final rows using all of the active filters\n    return filterRows(rowModel.rows, filterRowsImpl, table);\n  }, getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () => table._autoResetPageIndex()));\n}\n\nfunction getGroupedRowModel() {\n  return table => memo(() => [table.getState().grouping, table.getPreGroupedRowModel()], (grouping, rowModel) => {\n    if (!rowModel.rows.length || !grouping.length) {\n      rowModel.rows.forEach(row => {\n        row.depth = 0;\n        row.parentId = undefined;\n      });\n      return rowModel;\n    }\n\n    // Filter the grouping list down to columns that exist\n    const existingGrouping = grouping.filter(columnId => table.getColumn(columnId));\n    const groupedFlatRows = [];\n    const groupedRowsById = {};\n    // const onlyGroupedFlatRows: Row[] = [];\n    // const onlyGroupedRowsById: Record<RowId, Row> = {};\n    // const nonGroupedFlatRows: Row[] = [];\n    // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n    // Recursively group the data\n    const groupUpRecursively = function (rows, depth, parentId) {\n      if (depth === void 0) {\n        depth = 0;\n      }\n      // Grouping depth has been been met\n      // Stop grouping and simply rewrite thd depth and row relationships\n      if (depth >= existingGrouping.length) {\n        return rows.map(row => {\n          row.depth = depth;\n          groupedFlatRows.push(row);\n          groupedRowsById[row.id] = row;\n          if (row.subRows) {\n            row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id);\n          }\n          return row;\n        });\n      }\n      const columnId = existingGrouping[depth];\n\n      // Group the rows together for this level\n      const rowGroupsMap = groupBy(rows, columnId);\n\n      // Perform aggregations for each group\n      const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map((_ref, index) => {\n        let [groupingValue, groupedRows] = _ref;\n        let id = `${columnId}:${groupingValue}`;\n        id = parentId ? `${parentId}>${id}` : id;\n\n        // First, Recurse to group sub rows before aggregation\n        const subRows = groupUpRecursively(groupedRows, depth + 1, id);\n        subRows.forEach(subRow => {\n          subRow.parentId = id;\n        });\n\n        // Flatten the leaf rows of the rows in this group\n        const leafRows = depth ? flattenBy(groupedRows, row => row.subRows) : groupedRows;\n        const row = createRow(table, id, leafRows[0].original, index, depth, undefined, parentId);\n        Object.assign(row, {\n          groupingColumnId: columnId,\n          groupingValue,\n          subRows,\n          leafRows,\n          getValue: columnId => {\n            // Don't aggregate columns that are in the grouping\n            if (existingGrouping.includes(columnId)) {\n              if (row._valuesCache.hasOwnProperty(columnId)) {\n                return row._valuesCache[columnId];\n              }\n              if (groupedRows[0]) {\n                var _groupedRows$0$getVal;\n                row._valuesCache[columnId] = (_groupedRows$0$getVal = groupedRows[0].getValue(columnId)) != null ? _groupedRows$0$getVal : undefined;\n              }\n              return row._valuesCache[columnId];\n            }\n            if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n              return row._groupingValuesCache[columnId];\n            }\n\n            // Aggregate the values\n            const column = table.getColumn(columnId);\n            const aggregateFn = column == null ? void 0 : column.getAggregationFn();\n            if (aggregateFn) {\n              row._groupingValuesCache[columnId] = aggregateFn(columnId, leafRows, groupedRows);\n              return row._groupingValuesCache[columnId];\n            }\n          }\n        });\n        subRows.forEach(subRow => {\n          groupedFlatRows.push(subRow);\n          groupedRowsById[subRow.id] = subRow;\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        });\n        return row;\n      });\n      return aggregatedGroupedRows;\n    };\n    const groupedRows = groupUpRecursively(rowModel.rows, 0);\n    groupedRows.forEach(subRow => {\n      groupedFlatRows.push(subRow);\n      groupedRowsById[subRow.id] = subRow;\n      // if (subRow.getIsGrouped?.()) {\n      //   onlyGroupedFlatRows.push(subRow);\n      //   onlyGroupedRowsById[subRow.id] = subRow;\n      // } else {\n      //   nonGroupedFlatRows.push(subRow);\n      //   nonGroupedRowsById[subRow.id] = subRow;\n      // }\n    });\n    return {\n      rows: groupedRows,\n      flatRows: groupedFlatRows,\n      rowsById: groupedRowsById\n    };\n  }, getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n    table._queue(() => {\n      table._autoResetExpanded();\n      table._autoResetPageIndex();\n    });\n  }));\n}\nfunction groupBy(rows, columnId) {\n  const groupMap = new Map();\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`;\n    const previous = map.get(resKey);\n    if (!previous) {\n      map.set(resKey, [row]);\n    } else {\n      previous.push(row);\n    }\n    return map;\n  }, groupMap);\n}\n\nfunction getPaginationRowModel(opts) {\n  return table => memo(() => [table.getState().pagination, table.getPrePaginationRowModel(), table.options.paginateExpandedRows ? undefined : table.getState().expanded], (pagination, rowModel) => {\n    if (!rowModel.rows.length) {\n      return rowModel;\n    }\n    const {\n      pageSize,\n      pageIndex\n    } = pagination;\n    let {\n      rows,\n      flatRows,\n      rowsById\n    } = rowModel;\n    const pageStart = pageSize * pageIndex;\n    const pageEnd = pageStart + pageSize;\n    rows = rows.slice(pageStart, pageEnd);\n    let paginatedRowModel;\n    if (!table.options.paginateExpandedRows) {\n      paginatedRowModel = expandRows({\n        rows,\n        flatRows,\n        rowsById\n      });\n    } else {\n      paginatedRowModel = {\n        rows,\n        flatRows,\n        rowsById\n      };\n    }\n    paginatedRowModel.flatRows = [];\n    const handleRow = row => {\n      paginatedRowModel.flatRows.push(row);\n      if (row.subRows.length) {\n        row.subRows.forEach(handleRow);\n      }\n    };\n    paginatedRowModel.rows.forEach(handleRow);\n    return paginatedRowModel;\n  }, getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel'));\n}\n\nfunction getSortedRowModel() {\n  return table => memo(() => [table.getState().sorting, table.getPreSortedRowModel()], (sorting, rowModel) => {\n    if (!rowModel.rows.length || !(sorting != null && sorting.length)) {\n      return rowModel;\n    }\n    const sortingState = table.getState().sorting;\n    const sortedFlatRows = [];\n\n    // Filter out sortings that correspond to non existing columns\n    const availableSorting = sortingState.filter(sort => {\n      var _table$getColumn;\n      return (_table$getColumn = table.getColumn(sort.id)) == null ? void 0 : _table$getColumn.getCanSort();\n    });\n    const columnInfoById = {};\n    availableSorting.forEach(sortEntry => {\n      const column = table.getColumn(sortEntry.id);\n      if (!column) return;\n      columnInfoById[sortEntry.id] = {\n        sortUndefined: column.columnDef.sortUndefined,\n        invertSorting: column.columnDef.invertSorting,\n        sortingFn: column.getSortingFn()\n      };\n    });\n    const sortData = rows => {\n      // This will also perform a stable sorting using the row index\n      // if needed.\n      const sortedData = rows.map(row => ({\n        ...row\n      }));\n      sortedData.sort((rowA, rowB) => {\n        for (let i = 0; i < availableSorting.length; i += 1) {\n          var _sortEntry$desc;\n          const sortEntry = availableSorting[i];\n          const columnInfo = columnInfoById[sortEntry.id];\n          const sortUndefined = columnInfo.sortUndefined;\n          const isDesc = (_sortEntry$desc = sortEntry == null ? void 0 : sortEntry.desc) != null ? _sortEntry$desc : false;\n          let sortInt = 0;\n\n          // All sorting ints should always return in ascending order\n          if (sortUndefined) {\n            const aValue = rowA.getValue(sortEntry.id);\n            const bValue = rowB.getValue(sortEntry.id);\n            const aUndefined = aValue === undefined;\n            const bUndefined = bValue === undefined;\n            if (aUndefined || bUndefined) {\n              if (sortUndefined === 'first') return aUndefined ? -1 : 1;\n              if (sortUndefined === 'last') return aUndefined ? 1 : -1;\n              sortInt = aUndefined && bUndefined ? 0 : aUndefined ? sortUndefined : -sortUndefined;\n            }\n          }\n          if (sortInt === 0) {\n            sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id);\n          }\n\n          // If sorting is non-zero, take care of desc and inversion\n          if (sortInt !== 0) {\n            if (isDesc) {\n              sortInt *= -1;\n            }\n            if (columnInfo.invertSorting) {\n              sortInt *= -1;\n            }\n            return sortInt;\n          }\n        }\n        return rowA.index - rowB.index;\n      });\n\n      // If there are sub-rows, sort them\n      sortedData.forEach(row => {\n        var _row$subRows;\n        sortedFlatRows.push(row);\n        if ((_row$subRows = row.subRows) != null && _row$subRows.length) {\n          row.subRows = sortData(row.subRows);\n        }\n      });\n      return sortedData;\n    };\n    return {\n      rows: sortData(rowModel.rows),\n      flatRows: sortedFlatRows,\n      rowsById: rowModel.rowsById\n    };\n  }, getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () => table._autoResetPageIndex()));\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs\n");

/***/ })

};
;