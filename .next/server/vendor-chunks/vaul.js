"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vaul";
exports.ids = ["vendor-chunks/vaul"];
exports.modules = {

/***/ "(ssr)/./node_modules/vaul/dist/index.mjs":
/*!******************************************!*\
  !*** ./node_modules/vaul/dist/index.mjs ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Drawer: () => (/* binding */ Drawer),\n/* harmony export */   Handle: () => (/* binding */ Handle),\n/* harmony export */   NestedRoot: () => (/* binding */ NestedRoot),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Content,Drawer,Handle,NestedRoot,Overlay,Portal,Root auto */ function __insertCSS(code) {\n    if (!code || typeof document == 'undefined') return;\n    let head = document.head || document.getElementsByTagName('head')[0];\n    let style = document.createElement('style');\n    style.type = 'text/css';\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\n\nconst DrawerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    drawerRef: {\n        current: null\n    },\n    overlayRef: {\n        current: null\n    },\n    onPress: ()=>{},\n    onRelease: ()=>{},\n    onDrag: ()=>{},\n    onNestedDrag: ()=>{},\n    onNestedOpenChange: ()=>{},\n    onNestedRelease: ()=>{},\n    openProp: undefined,\n    dismissible: false,\n    isOpen: false,\n    isDragging: false,\n    keyboardIsOpen: {\n        current: false\n    },\n    snapPointsOffset: null,\n    snapPoints: null,\n    handleOnly: false,\n    modal: false,\n    shouldFade: false,\n    activeSnapPoint: null,\n    onOpenChange: ()=>{},\n    setActiveSnapPoint: ()=>{},\n    closeDrawer: ()=>{},\n    direction: 'bottom',\n    shouldAnimate: {\n        current: true\n    },\n    shouldScaleBackground: false,\n    setBackgroundColorOnScale: true,\n    noBodyStyles: false,\n    container: null,\n    autoFocus: false\n});\nconst useDrawerContext = ()=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DrawerContext);\n    if (!context) {\n        throw new Error('useDrawerContext must be used within a Drawer.Root');\n    }\n    return context;\n};\n__insertCSS(\"[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--initial-transform,100%),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--initial-transform,100%),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-animate=false]{animation:none!important}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\\n[data-state=closed]\\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,var(--initial-transform,100%),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,var(--initial-transform,100%),0)}}@keyframes slideFromTop{from{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}}@keyframes slideFromLeft{from{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}}@keyframes slideFromRight{from{transform:translate3d(var(--initial-transform,100%),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(var(--initial-transform,100%),0,0)}}\");\nfunction isMobileFirefox() {\n    const userAgent = navigator.userAgent;\n    return  false && (0 // iOS Firefox\n    );\n}\nfunction isMac() {\n    return testPlatform(/^Mac/);\n}\nfunction isIPhone() {\n    return testPlatform(/^iPhone/);\n}\nfunction isSafari() {\n    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n}\nfunction isIPad() {\n    return testPlatform(/^iPad/) || // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    isMac() && navigator.maxTouchPoints > 1;\n}\nfunction isIOS() {\n    return isIPhone() || isIPad();\n}\nfunction testPlatform(re) {\n    return  false ? 0 : undefined;\n}\n// This code comes from https://github.com/adobe/react-spectrum/blob/main/packages/%40react-aria/overlays/src/usePreventScroll.ts\nconst KEYBOARD_BUFFER = 24;\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction chain$1(...callbacks) {\n    return (...args)=>{\n        for (let callback of callbacks){\n            if (typeof callback === 'function') {\n                callback(...args);\n            }\n        }\n    };\n}\n// @ts-ignore\nconst visualViewport = typeof document !== 'undefined' && window.visualViewport;\nfunction isScrollable(node) {\n    let style = window.getComputedStyle(node);\n    return /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n}\nfunction getScrollParent(node) {\n    if (isScrollable(node)) {\n        node = node.parentElement;\n    }\n    while(node && !isScrollable(node)){\n        node = node.parentElement;\n    }\n    return node || document.scrollingElement || document.documentElement;\n}\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n    'checkbox',\n    'radio',\n    'range',\n    'color',\n    'file',\n    'image',\n    'button',\n    'submit',\n    'reset'\n]);\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */ function usePreventScroll(options = {}) {\n    let { isDisabled } = options;\n    useIsomorphicLayoutEffect({\n        \"usePreventScroll.useIsomorphicLayoutEffect\": ()=>{\n            if (isDisabled) {\n                return;\n            }\n            preventScrollCount++;\n            if (preventScrollCount === 1) {\n                if (isIOS()) {\n                    restore = preventScrollMobileSafari();\n                }\n            }\n            return ({\n                \"usePreventScroll.useIsomorphicLayoutEffect\": ()=>{\n                    preventScrollCount--;\n                    if (preventScrollCount === 0) {\n                        restore == null ? void 0 : restore();\n                    }\n                }\n            })[\"usePreventScroll.useIsomorphicLayoutEffect\"];\n        }\n    }[\"usePreventScroll.useIsomorphicLayoutEffect\"], [\n        isDisabled\n    ]);\n}\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Prevent default on `touchmove` events inside a scrollable element when the scroll position is at the\n//    top or bottom. This avoids the whole page scrolling instead, but does prevent overscrolling.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n    let scrollable;\n    let lastY = 0;\n    let onTouchStart = (e)=>{\n        // Store the nearest scrollable parent element from the element that the user touched.\n        scrollable = getScrollParent(e.target);\n        if (scrollable === document.documentElement && scrollable === document.body) {\n            return;\n        }\n        lastY = e.changedTouches[0].pageY;\n    };\n    let onTouchMove = (e)=>{\n        // Prevent scrolling the window.\n        if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n            e.preventDefault();\n            return;\n        }\n        // Prevent scrolling up when at the top and scrolling down when at the bottom\n        // of a nested scrollable area, otherwise mobile Safari will start scrolling\n        // the window instead. Unfortunately, this disables bounce scrolling when at\n        // the top but it's the best we can do.\n        let y = e.changedTouches[0].pageY;\n        let scrollTop = scrollable.scrollTop;\n        let bottom = scrollable.scrollHeight - scrollable.clientHeight;\n        if (bottom === 0) {\n            return;\n        }\n        if (scrollTop <= 0 && y > lastY || scrollTop >= bottom && y < lastY) {\n            e.preventDefault();\n        }\n        lastY = y;\n    };\n    let onTouchEnd = (e)=>{\n        let target = e.target;\n        // Apply this change if we're not already focused on the target element\n        if (isInput(target) && target !== document.activeElement) {\n            e.preventDefault();\n            // Apply a transform to trick Safari into thinking the input is at the top of the page\n            // so it doesn't try to scroll it into view. When tapping on an input, this needs to\n            // be done before the \"focus\" event, so we have to focus the element ourselves.\n            target.style.transform = 'translateY(-2000px)';\n            target.focus();\n            requestAnimationFrame(()=>{\n                target.style.transform = '';\n            });\n        }\n    };\n    let onFocus = (e)=>{\n        let target = e.target;\n        if (isInput(target)) {\n            // Transform also needs to be applied in the focus event in cases where focus moves\n            // other than tapping on an input directly, e.g. the next/previous buttons in the\n            // software keyboard. In these cases, it seems applying the transform in the focus event\n            // is good enough, whereas when tapping an input, it must be done before the focus event. 🤷‍♂️\n            target.style.transform = 'translateY(-2000px)';\n            requestAnimationFrame(()=>{\n                target.style.transform = '';\n                // This will have prevented the browser from scrolling the focused element into view,\n                // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n                if (visualViewport) {\n                    if (visualViewport.height < window.innerHeight) {\n                        // If the keyboard is already visible, do this after one additional frame\n                        // to wait for the transform to be removed.\n                        requestAnimationFrame(()=>{\n                            scrollIntoView(target);\n                        });\n                    } else {\n                        // Otherwise, wait for the visual viewport to resize before scrolling so we can\n                        // measure the correct position to scroll to.\n                        visualViewport.addEventListener('resize', ()=>scrollIntoView(target), {\n                            once: true\n                        });\n                    }\n                }\n            });\n        }\n    };\n    let onWindowScroll = ()=>{\n        // Last resort. If the window scrolled, scroll it back to the top.\n        // It should always be at the top because the body will have a negative margin (see below).\n        window.scrollTo(0, 0);\n    };\n    // Record the original scroll position so we can restore it.\n    // Then apply a negative margin to the body to offset it by the scroll position. This will\n    // enable us to scroll the window to the top, which is required for the rest of this to work.\n    let scrollX = window.pageXOffset;\n    let scrollY = window.pageYOffset;\n    let restoreStyles = chain$1(setStyle(document.documentElement, 'paddingRight', `${window.innerWidth - document.documentElement.clientWidth}px`));\n    // Scroll to the top. The negative margin on the body will make this appear the same.\n    window.scrollTo(0, 0);\n    let removeEvents = chain$1(addEvent(document, 'touchstart', onTouchStart, {\n        passive: false,\n        capture: true\n    }), addEvent(document, 'touchmove', onTouchMove, {\n        passive: false,\n        capture: true\n    }), addEvent(document, 'touchend', onTouchEnd, {\n        passive: false,\n        capture: true\n    }), addEvent(document, 'focus', onFocus, true), addEvent(window, 'scroll', onWindowScroll));\n    return ()=>{\n        // Restore styles and scroll the page back to where it was.\n        restoreStyles();\n        removeEvents();\n        window.scrollTo(scrollX, scrollY);\n    };\n}\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element, style, value) {\n    // https://github.com/microsoft/TypeScript/issues/17827#issuecomment-391663310\n    // @ts-ignore\n    let cur = element.style[style];\n    // @ts-ignore\n    element.style[style] = value;\n    return ()=>{\n        // @ts-ignore\n        element.style[style] = cur;\n    };\n}\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent(target, event, handler, options) {\n    // @ts-ignore\n    target.addEventListener(event, handler, options);\n    return ()=>{\n        // @ts-ignore\n        target.removeEventListener(event, handler, options);\n    };\n}\nfunction scrollIntoView(target) {\n    let root = document.scrollingElement || document.documentElement;\n    while(target && target !== root){\n        // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n        let scrollable = getScrollParent(target);\n        if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== target) {\n            let scrollableTop = scrollable.getBoundingClientRect().top;\n            let targetTop = target.getBoundingClientRect().top;\n            let targetBottom = target.getBoundingClientRect().bottom;\n            // Buffer is needed for some edge cases\n            const keyboardHeight = scrollable.getBoundingClientRect().bottom + KEYBOARD_BUFFER;\n            if (targetBottom > keyboardHeight) {\n                scrollable.scrollTop += targetTop - scrollableTop;\n            }\n        }\n        // @ts-ignore\n        target = scrollable.parentElement;\n    }\n}\nfunction isInput(target) {\n    return target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type) || target instanceof HTMLTextAreaElement || target instanceof HTMLElement && target.isContentEditable;\n}\n// This code comes from https://github.com/radix-ui/primitives/tree/main/packages/react/compose-refs\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */ function setRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    } else if (ref !== null && ref !== undefined) {\n        ref.current = value;\n    }\n}\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */ function composeRefs(...refs) {\n    return (node)=>refs.forEach((ref)=>setRef(ref, node));\n}\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */ function useComposedRefs(...refs) {\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\nconst cache = new WeakMap();\nfunction set(el, styles, ignoreCache = false) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = {};\n    Object.entries(styles).forEach(([key, value])=>{\n        if (key.startsWith('--')) {\n            el.style.setProperty(key, value);\n            return;\n        }\n        originalStyles[key] = el.style[key];\n        el.style[key] = value;\n    });\n    if (ignoreCache) return;\n    cache.set(el, originalStyles);\n}\nfunction reset(el, prop) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = cache.get(el);\n    if (!originalStyles) {\n        return;\n    }\n    {\n        el.style[prop] = originalStyles[prop];\n    }\n}\nconst isVertical = (direction)=>{\n    switch(direction){\n        case 'top':\n        case 'bottom':\n            return true;\n        case 'left':\n        case 'right':\n            return false;\n        default:\n            return direction;\n    }\n};\nfunction getTranslate(element, direction) {\n    if (!element) {\n        return null;\n    }\n    const style = window.getComputedStyle(element);\n    const transform = style.transform || style.webkitTransform || style.mozTransform;\n    let mat = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (mat) {\n        // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix3d\n        return parseFloat(mat[1].split(', ')[isVertical(direction) ? 13 : 12]);\n    }\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix\n    mat = transform.match(/^matrix\\((.+)\\)$/);\n    return mat ? parseFloat(mat[1].split(', ')[isVertical(direction) ? 5 : 4]) : null;\n}\nfunction dampenValue(v) {\n    return 8 * (Math.log(v + 1) - 2);\n}\nfunction assignStyle(element, style) {\n    if (!element) return ()=>{};\n    const prevStyle = element.style.cssText;\n    Object.assign(element.style, style);\n    return ()=>{\n        element.style.cssText = prevStyle;\n    };\n}\n/**\n * Receives functions as arguments and returns a new function that calls all.\n */ function chain(...fns) {\n    return (...args)=>{\n        for (const fn of fns){\n            if (typeof fn === 'function') {\n                // @ts-ignore\n                fn(...args);\n            }\n        }\n    };\n}\nconst TRANSITIONS = {\n    DURATION: 0.5,\n    EASE: [\n        0.32,\n        0.72,\n        0,\n        1\n    ]\n};\nconst VELOCITY_THRESHOLD = 0.4;\nconst CLOSE_THRESHOLD = 0.25;\nconst SCROLL_LOCK_TIMEOUT = 100;\nconst BORDER_RADIUS = 8;\nconst NESTED_DISPLACEMENT = 16;\nconst WINDOW_TOP_OFFSET = 26;\nconst DRAG_CLASS = 'vaul-dragging';\n// This code comes from https://github.com/radix-ui/primitives/blob/main/packages/react/use-controllable-state/src/useControllableState.tsx\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useCallbackRef.useEffect\": ()=>{\n            callbackRef.current = callback;\n        }\n    }[\"useCallbackRef.useEffect\"]);\n    // https://github.com/facebook/react/issues/19240\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useCallbackRef.useMemo\": ()=>({\n                \"useCallbackRef.useMemo\": (...args)=>callbackRef.current == null ? void 0 : callbackRef.current.call(callbackRef, ...args)\n            })[\"useCallbackRef.useMemo\"]\n    }[\"useCallbackRef.useMemo\"], []);\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const [value] = uncontrolledState;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const handleChange = useCallbackRef(onChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useUncontrolledState.useEffect\": ()=>{\n            if (prevValueRef.current !== value) {\n                handleChange(value);\n                prevValueRef.current = value;\n            }\n        }\n    }[\"useUncontrolledState.useEffect\"], [\n        value,\n        prevValueRef,\n        handleChange\n    ]);\n    return uncontrolledState;\n}\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{} }) {\n    const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== undefined;\n    const value = isControlled ? prop : uncontrolledProp;\n    const handleChange = useCallbackRef(onChange);\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useControllableState.useCallback[setValue]\": (nextValue)=>{\n            if (isControlled) {\n                const setter = nextValue;\n                const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n                if (value !== prop) handleChange(value);\n            } else {\n                setUncontrolledProp(nextValue);\n            }\n        }\n    }[\"useControllableState.useCallback[setValue]\"], [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        handleChange\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useSnapPoints({ activeSnapPointProp, setActiveSnapPointProp, snapPoints, drawerRef, overlayRef, fadeFromIndex, onSnapPointChange, direction = 'bottom', container, snapToSequentialPoint }) {\n    const [activeSnapPoint, setActiveSnapPoint] = useControllableState({\n        prop: activeSnapPointProp,\n        defaultProp: snapPoints == null ? void 0 : snapPoints[0],\n        onChange: setActiveSnapPointProp\n    });\n    const [windowDimensions, setWindowDimensions] = react__WEBPACK_IMPORTED_MODULE_0__.useState( false ? 0 : undefined);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSnapPoints.useEffect\": ()=>{\n            function onResize() {\n                setWindowDimensions({\n                    innerWidth: window.innerWidth,\n                    innerHeight: window.innerHeight\n                });\n            }\n            window.addEventListener('resize', onResize);\n            return ({\n                \"useSnapPoints.useEffect\": ()=>window.removeEventListener('resize', onResize)\n            })[\"useSnapPoints.useEffect\"];\n        }\n    }[\"useSnapPoints.useEffect\"], []);\n    const isLastSnapPoint = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useSnapPoints.useMemo[isLastSnapPoint]\": ()=>activeSnapPoint === (snapPoints == null ? void 0 : snapPoints[snapPoints.length - 1]) || null\n    }[\"useSnapPoints.useMemo[isLastSnapPoint]\"], [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const activeSnapPointIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useSnapPoints.useMemo[activeSnapPointIndex]\": ()=>{\n            var _snapPoints_findIndex;\n            return (_snapPoints_findIndex = snapPoints == null ? void 0 : snapPoints.findIndex({\n                \"useSnapPoints.useMemo[activeSnapPointIndex]\": (snapPoint)=>snapPoint === activeSnapPoint\n            }[\"useSnapPoints.useMemo[activeSnapPointIndex]\"])) != null ? _snapPoints_findIndex : null;\n        }\n    }[\"useSnapPoints.useMemo[activeSnapPointIndex]\"], [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const shouldFade = snapPoints && snapPoints.length > 0 && (fadeFromIndex || fadeFromIndex === 0) && !Number.isNaN(fadeFromIndex) && snapPoints[fadeFromIndex] === activeSnapPoint || !snapPoints;\n    const snapPointsOffset = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useSnapPoints.useMemo[snapPointsOffset]\": ()=>{\n            const containerSize = container ? {\n                width: container.getBoundingClientRect().width,\n                height: container.getBoundingClientRect().height\n            } :  false ? 0 : {\n                width: 0,\n                height: 0\n            };\n            var _snapPoints_map;\n            return (_snapPoints_map = snapPoints == null ? void 0 : snapPoints.map({\n                \"useSnapPoints.useMemo[snapPointsOffset]\": (snapPoint)=>{\n                    const isPx = typeof snapPoint === 'string';\n                    let snapPointAsNumber = 0;\n                    if (isPx) {\n                        snapPointAsNumber = parseInt(snapPoint, 10);\n                    }\n                    if (isVertical(direction)) {\n                        const height = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.height : 0;\n                        if (windowDimensions) {\n                            return direction === 'bottom' ? containerSize.height - height : -containerSize.height + height;\n                        }\n                        return height;\n                    }\n                    const width = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.width : 0;\n                    if (windowDimensions) {\n                        return direction === 'right' ? containerSize.width - width : -containerSize.width + width;\n                    }\n                    return width;\n                }\n            }[\"useSnapPoints.useMemo[snapPointsOffset]\"])) != null ? _snapPoints_map : [];\n        }\n    }[\"useSnapPoints.useMemo[snapPointsOffset]\"], [\n        snapPoints,\n        windowDimensions,\n        container\n    ]);\n    const activeSnapPointOffset = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useSnapPoints.useMemo[activeSnapPointOffset]\": ()=>activeSnapPointIndex !== null ? snapPointsOffset == null ? void 0 : snapPointsOffset[activeSnapPointIndex] : null\n    }[\"useSnapPoints.useMemo[activeSnapPointOffset]\"], [\n        snapPointsOffset,\n        activeSnapPointIndex\n    ]);\n    const snapToPoint = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useSnapPoints.useCallback[snapToPoint]\": (dimension)=>{\n            var _snapPointsOffset_findIndex;\n            const newSnapPointIndex = (_snapPointsOffset_findIndex = snapPointsOffset == null ? void 0 : snapPointsOffset.findIndex({\n                \"useSnapPoints.useCallback[snapToPoint]\": (snapPointDim)=>snapPointDim === dimension\n            }[\"useSnapPoints.useCallback[snapToPoint]\"])) != null ? _snapPointsOffset_findIndex : null;\n            onSnapPointChange(newSnapPointIndex);\n            set(drawerRef.current, {\n                transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                transform: isVertical(direction) ? `translate3d(0, ${dimension}px, 0)` : `translate3d(${dimension}px, 0, 0)`\n            });\n            if (snapPointsOffset && newSnapPointIndex !== snapPointsOffset.length - 1 && fadeFromIndex !== undefined && newSnapPointIndex !== fadeFromIndex && newSnapPointIndex < fadeFromIndex) {\n                set(overlayRef.current, {\n                    transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                    opacity: '0'\n                });\n            } else {\n                set(overlayRef.current, {\n                    transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                    opacity: '1'\n                });\n            }\n            setActiveSnapPoint(snapPoints == null ? void 0 : snapPoints[Math.max(newSnapPointIndex, 0)]);\n        }\n    }[\"useSnapPoints.useCallback[snapToPoint]\"], [\n        drawerRef.current,\n        snapPoints,\n        snapPointsOffset,\n        fadeFromIndex,\n        overlayRef,\n        setActiveSnapPoint\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSnapPoints.useEffect\": ()=>{\n            if (activeSnapPoint || activeSnapPointProp) {\n                var _snapPoints_findIndex;\n                const newIndex = (_snapPoints_findIndex = snapPoints == null ? void 0 : snapPoints.findIndex({\n                    \"useSnapPoints.useEffect\": (snapPoint)=>snapPoint === activeSnapPointProp || snapPoint === activeSnapPoint\n                }[\"useSnapPoints.useEffect\"])) != null ? _snapPoints_findIndex : -1;\n                if (snapPointsOffset && newIndex !== -1 && typeof snapPointsOffset[newIndex] === 'number') {\n                    snapToPoint(snapPointsOffset[newIndex]);\n                }\n            }\n        }\n    }[\"useSnapPoints.useEffect\"], [\n        activeSnapPoint,\n        activeSnapPointProp,\n        snapPoints,\n        snapPointsOffset,\n        snapToPoint\n    ]);\n    function onRelease({ draggedDistance, closeDrawer, velocity, dismissible }) {\n        if (fadeFromIndex === undefined) return;\n        const currentPosition = direction === 'bottom' || direction === 'right' ? (activeSnapPointOffset != null ? activeSnapPointOffset : 0) - draggedDistance : (activeSnapPointOffset != null ? activeSnapPointOffset : 0) + draggedDistance;\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isFirst = activeSnapPointIndex === 0;\n        const hasDraggedUp = draggedDistance > 0;\n        if (isOverlaySnapPoint) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n            });\n        }\n        if (!snapToSequentialPoint && velocity > 2 && !hasDraggedUp) {\n            if (dismissible) closeDrawer();\n            else snapToPoint(snapPointsOffset[0]); // snap to initial point\n            return;\n        }\n        if (!snapToSequentialPoint && velocity > 2 && hasDraggedUp && snapPointsOffset && snapPoints) {\n            snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n            return;\n        }\n        // Find the closest snap point to the current position\n        const closestSnapPoint = snapPointsOffset == null ? void 0 : snapPointsOffset.reduce((prev, curr)=>{\n            if (typeof prev !== 'number' || typeof curr !== 'number') return prev;\n            return Math.abs(curr - currentPosition) < Math.abs(prev - currentPosition) ? curr : prev;\n        });\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        if (velocity > VELOCITY_THRESHOLD && Math.abs(draggedDistance) < dim * 0.4) {\n            const dragDirection = hasDraggedUp ? 1 : -1; // 1 = up, -1 = down\n            // Don't do anything if we swipe upwards while being on the last snap point\n            if (dragDirection > 0 && isLastSnapPoint && snapPoints) {\n                snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n                return;\n            }\n            if (isFirst && dragDirection < 0 && dismissible) {\n                closeDrawer();\n            }\n            if (activeSnapPointIndex === null) return;\n            snapToPoint(snapPointsOffset[activeSnapPointIndex + dragDirection]);\n            return;\n        }\n        snapToPoint(closestSnapPoint);\n    }\n    function onDrag({ draggedDistance }) {\n        if (activeSnapPointOffset === null) return;\n        const newValue = direction === 'bottom' || direction === 'right' ? activeSnapPointOffset - draggedDistance : activeSnapPointOffset + draggedDistance;\n        // Don't do anything if we exceed the last(biggest) snap point\n        if ((direction === 'bottom' || direction === 'right') && newValue < snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        if ((direction === 'top' || direction === 'left') && newValue > snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `translate3d(0, ${newValue}px, 0)` : `translate3d(${newValue}px, 0, 0)`\n        });\n    }\n    function getPercentageDragged(absDraggedDistance, isDraggingDown) {\n        if (!snapPoints || typeof activeSnapPointIndex !== 'number' || !snapPointsOffset || fadeFromIndex === undefined) return null;\n        // If this is true we are dragging to a snap point that is supposed to have an overlay\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isOverlaySnapPointOrHigher = activeSnapPointIndex >= fadeFromIndex;\n        if (isOverlaySnapPointOrHigher && isDraggingDown) {\n            return 0;\n        }\n        // Don't animate, but still use this one if we are dragging away from the overlaySnapPoint\n        if (isOverlaySnapPoint && !isDraggingDown) return 1;\n        if (!shouldFade && !isOverlaySnapPoint) return null;\n        // Either fadeFrom index or the one before\n        const targetSnapPointIndex = isOverlaySnapPoint ? activeSnapPointIndex + 1 : activeSnapPointIndex - 1;\n        // Get the distance from overlaySnapPoint to the one before or vice-versa to calculate the opacity percentage accordingly\n        const snapPointDistance = isOverlaySnapPoint ? snapPointsOffset[targetSnapPointIndex] - snapPointsOffset[targetSnapPointIndex - 1] : snapPointsOffset[targetSnapPointIndex + 1] - snapPointsOffset[targetSnapPointIndex];\n        const percentageDragged = absDraggedDistance / Math.abs(snapPointDistance);\n        if (isOverlaySnapPoint) {\n            return 1 - percentageDragged;\n        } else {\n            return percentageDragged;\n        }\n    }\n    return {\n        isLastSnapPoint,\n        activeSnapPoint,\n        shouldFade,\n        getPercentageDragged,\n        setActiveSnapPoint,\n        activeSnapPointIndex,\n        onRelease,\n        onDrag,\n        snapPointsOffset\n    };\n}\nconst noop = ()=>()=>{};\nfunction useScaleBackground() {\n    const { direction, isOpen, shouldScaleBackground, setBackgroundColorOnScale, noBodyStyles } = useDrawerContext();\n    const timeoutIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const initialBackgroundColor = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useScaleBackground.useMemo[initialBackgroundColor]\": ()=>document.body.style.backgroundColor\n    }[\"useScaleBackground.useMemo[initialBackgroundColor]\"], []);\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useScaleBackground.useEffect\": ()=>{\n            if (isOpen && shouldScaleBackground) {\n                if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current);\n                const wrapper = document.querySelector('[data-vaul-drawer-wrapper]') || document.querySelector('[vaul-drawer-wrapper]');\n                if (!wrapper) return;\n                chain(setBackgroundColorOnScale && !noBodyStyles ? assignStyle(document.body, {\n                    background: 'black'\n                }) : noop, assignStyle(wrapper, {\n                    transformOrigin: isVertical(direction) ? 'top' : 'left',\n                    transitionProperty: 'transform, border-radius',\n                    transitionDuration: `${TRANSITIONS.DURATION}s`,\n                    transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n                }));\n                const wrapperStylesCleanup = assignStyle(wrapper, {\n                    borderRadius: `${BORDER_RADIUS}px`,\n                    overflow: 'hidden',\n                    ...isVertical(direction) ? {\n                        transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`\n                    } : {\n                        transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`\n                    }\n                });\n                return ({\n                    \"useScaleBackground.useEffect\": ()=>{\n                        wrapperStylesCleanup();\n                        timeoutIdRef.current = window.setTimeout({\n                            \"useScaleBackground.useEffect\": ()=>{\n                                if (initialBackgroundColor) {\n                                    document.body.style.background = initialBackgroundColor;\n                                } else {\n                                    document.body.style.removeProperty('background');\n                                }\n                            }\n                        }[\"useScaleBackground.useEffect\"], TRANSITIONS.DURATION * 1000);\n                    }\n                })[\"useScaleBackground.useEffect\"];\n            }\n        }\n    }[\"useScaleBackground.useEffect\"], [\n        isOpen,\n        shouldScaleBackground,\n        initialBackgroundColor\n    ]);\n}\nlet previousBodyPosition = null;\n/**\n * This hook is necessary to prevent buggy behavior on iOS devices (need to test on Android).\n * I won't get into too much detail about what bugs it solves, but so far I've found that setting the body to `position: fixed` is the most reliable way to prevent those bugs.\n * Issues that this hook solves:\n * https://github.com/emilkowalski/vaul/issues/435\n * https://github.com/emilkowalski/vaul/issues/433\n * And more that I discovered, but were just not reported.\n */ function usePositionFixed({ isOpen, modal, nested, hasBeenOpened, preventScrollRestoration, noBodyStyles }) {\n    const [activeUrl, setActiveUrl] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"usePositionFixed.useState\": ()=> false ? 0 : ''\n    }[\"usePositionFixed.useState\"]);\n    const scrollPos = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const setPositionFixed = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"usePositionFixed.useCallback[setPositionFixed]\": ()=>{\n            // All browsers on iOS will return true here.\n            if (!isSafari()) return;\n            // If previousBodyPosition is already set, don't set it again.\n            if (previousBodyPosition === null && isOpen && !noBodyStyles) {\n                previousBodyPosition = {\n                    position: document.body.style.position,\n                    top: document.body.style.top,\n                    left: document.body.style.left,\n                    height: document.body.style.height,\n                    right: 'unset'\n                };\n                // Update the dom inside an animation frame\n                const { scrollX, innerHeight } = window;\n                document.body.style.setProperty('position', 'fixed', 'important');\n                Object.assign(document.body.style, {\n                    top: `${-scrollPos.current}px`,\n                    left: `${-scrollX}px`,\n                    right: '0px',\n                    height: 'auto'\n                });\n                window.setTimeout({\n                    \"usePositionFixed.useCallback[setPositionFixed]\": ()=>window.requestAnimationFrame({\n                            \"usePositionFixed.useCallback[setPositionFixed]\": ()=>{\n                                // Attempt to check if the bottom bar appeared due to the position change\n                                const bottomBarHeight = innerHeight - window.innerHeight;\n                                if (bottomBarHeight && scrollPos.current >= innerHeight) {\n                                    // Move the content further up so that the bottom bar doesn't hide it\n                                    document.body.style.top = `${-(scrollPos.current + bottomBarHeight)}px`;\n                                }\n                            }\n                        }[\"usePositionFixed.useCallback[setPositionFixed]\"])\n                }[\"usePositionFixed.useCallback[setPositionFixed]\"], 300);\n            }\n        }\n    }[\"usePositionFixed.useCallback[setPositionFixed]\"], [\n        isOpen\n    ]);\n    const restorePositionSetting = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"usePositionFixed.useCallback[restorePositionSetting]\": ()=>{\n            // All browsers on iOS will return true here.\n            if (!isSafari()) return;\n            if (previousBodyPosition !== null && !noBodyStyles) {\n                // Convert the position from \"px\" to Int\n                const y = -parseInt(document.body.style.top, 10);\n                const x = -parseInt(document.body.style.left, 10);\n                // Restore styles\n                Object.assign(document.body.style, previousBodyPosition);\n                window.requestAnimationFrame({\n                    \"usePositionFixed.useCallback[restorePositionSetting]\": ()=>{\n                        if (preventScrollRestoration && activeUrl !== window.location.href) {\n                            setActiveUrl(window.location.href);\n                            return;\n                        }\n                        window.scrollTo(x, y);\n                    }\n                }[\"usePositionFixed.useCallback[restorePositionSetting]\"]);\n                previousBodyPosition = null;\n            }\n        }\n    }[\"usePositionFixed.useCallback[restorePositionSetting]\"], [\n        activeUrl\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePositionFixed.useEffect\": ()=>{\n            function onScroll() {\n                scrollPos.current = window.scrollY;\n            }\n            onScroll();\n            window.addEventListener('scroll', onScroll);\n            return ({\n                \"usePositionFixed.useEffect\": ()=>{\n                    window.removeEventListener('scroll', onScroll);\n                }\n            })[\"usePositionFixed.useEffect\"];\n        }\n    }[\"usePositionFixed.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePositionFixed.useEffect\": ()=>{\n            if (!modal) return;\n            return ({\n                \"usePositionFixed.useEffect\": ()=>{\n                    if (typeof document === 'undefined') return;\n                    // Another drawer is opened, safe to ignore the execution\n                    const hasDrawerOpened = !!document.querySelector('[data-vaul-drawer]');\n                    if (hasDrawerOpened) return;\n                    restorePositionSetting();\n                }\n            })[\"usePositionFixed.useEffect\"];\n        }\n    }[\"usePositionFixed.useEffect\"], [\n        modal,\n        restorePositionSetting\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePositionFixed.useEffect\": ()=>{\n            if (nested || !hasBeenOpened) return;\n            // This is needed to force Safari toolbar to show **before** the drawer starts animating to prevent a gnarly shift from happening\n            if (isOpen) {\n                // avoid for standalone mode (PWA)\n                const isStandalone = window.matchMedia('(display-mode: standalone)').matches;\n                !isStandalone && setPositionFixed();\n                if (!modal) {\n                    window.setTimeout({\n                        \"usePositionFixed.useEffect\": ()=>{\n                            restorePositionSetting();\n                        }\n                    }[\"usePositionFixed.useEffect\"], 500);\n                }\n            } else {\n                restorePositionSetting();\n            }\n        }\n    }[\"usePositionFixed.useEffect\"], [\n        isOpen,\n        hasBeenOpened,\n        activeUrl,\n        modal,\n        nested,\n        setPositionFixed,\n        restorePositionSetting\n    ]);\n    return {\n        restorePositionSetting\n    };\n}\nfunction Root({ open: openProp, onOpenChange, children, onDrag: onDragProp, onRelease: onReleaseProp, snapPoints, shouldScaleBackground = false, setBackgroundColorOnScale = true, closeThreshold = CLOSE_THRESHOLD, scrollLockTimeout = SCROLL_LOCK_TIMEOUT, dismissible = true, handleOnly = false, fadeFromIndex = snapPoints && snapPoints.length - 1, activeSnapPoint: activeSnapPointProp, setActiveSnapPoint: setActiveSnapPointProp, fixed, modal = true, onClose, nested, noBodyStyles = false, direction = 'bottom', defaultOpen = false, disablePreventScroll = true, snapToSequentialPoint = false, preventScrollRestoration = false, repositionInputs = true, onAnimationEnd, container, autoFocus = false }) {\n    var _drawerRef_current, _drawerRef_current1;\n    const [isOpen = false, setIsOpen] = useControllableState({\n        defaultProp: defaultOpen,\n        prop: openProp,\n        onChange: {\n            \"Root.useControllableState\": (o)=>{\n                onOpenChange == null ? void 0 : onOpenChange(o);\n                if (!o && !nested) {\n                    restorePositionSetting();\n                }\n                setTimeout({\n                    \"Root.useControllableState\": ()=>{\n                        onAnimationEnd == null ? void 0 : onAnimationEnd(o);\n                    }\n                }[\"Root.useControllableState\"], TRANSITIONS.DURATION * 1000);\n                if (o && !modal) {\n                    if (false) {}\n                }\n                if (!o) {\n                    // This will be removed when the exit animation ends (`500ms`)\n                    document.body.style.pointerEvents = 'auto';\n                }\n            }\n        }[\"Root.useControllableState\"]\n    });\n    const [hasBeenOpened, setHasBeenOpened] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isDragging, setIsDragging] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [justReleased, setJustReleased] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const overlayRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const openTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const dragEndTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const lastTimeDragPrevented = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isAllowedToDrag = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const nestedOpenChangeTimer = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerStart = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const keyboardIsOpen = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldAnimate = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!defaultOpen);\n    const previousDiffFromInitial = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const drawerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const drawerHeightRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0);\n    const drawerWidthRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0);\n    const initialDrawerHeight = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const onSnapPointChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Root.useCallback[onSnapPointChange]\": (activeSnapPointIndex)=>{\n            // Change openTime ref when we reach the last snap point to prevent dragging for 500ms incase it's scrollable.\n            if (snapPoints && activeSnapPointIndex === snapPointsOffset.length - 1) openTime.current = new Date();\n        }\n    }[\"Root.useCallback[onSnapPointChange]\"], []);\n    const { activeSnapPoint, activeSnapPointIndex, setActiveSnapPoint, onRelease: onReleaseSnapPoints, snapPointsOffset, onDrag: onDragSnapPoints, shouldFade, getPercentageDragged: getSnapPointsPercentageDragged } = useSnapPoints({\n        snapPoints,\n        activeSnapPointProp,\n        setActiveSnapPointProp,\n        drawerRef,\n        fadeFromIndex,\n        overlayRef,\n        onSnapPointChange,\n        direction,\n        container,\n        snapToSequentialPoint\n    });\n    usePreventScroll({\n        isDisabled: !isOpen || isDragging || !modal || justReleased || !hasBeenOpened || !repositionInputs || !disablePreventScroll\n    });\n    const { restorePositionSetting } = usePositionFixed({\n        isOpen,\n        modal,\n        nested: nested != null ? nested : false,\n        hasBeenOpened,\n        preventScrollRestoration,\n        noBodyStyles\n    });\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    function onPress(event) {\n        var _drawerRef_current, _drawerRef_current1;\n        if (!dismissible && !snapPoints) return;\n        if (drawerRef.current && !drawerRef.current.contains(event.target)) return;\n        drawerHeightRef.current = ((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0;\n        drawerWidthRef.current = ((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0;\n        setIsDragging(true);\n        dragStartTime.current = new Date();\n        // iOS doesn't trigger mouseUp after scrolling so we need to listen to touched in order to disallow dragging\n        if (isIOS()) {\n            window.addEventListener('touchend', ()=>isAllowedToDrag.current = false, {\n                once: true\n            });\n        }\n        // Ensure we maintain correct pointer capture even when going outside of the drawer\n        event.target.setPointerCapture(event.pointerId);\n        pointerStart.current = isVertical(direction) ? event.pageY : event.pageX;\n    }\n    function shouldDrag(el, isDraggingInDirection) {\n        var _window_getSelection;\n        let element = el;\n        const highlightedText = (_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString();\n        const swipeAmount = drawerRef.current ? getTranslate(drawerRef.current, direction) : null;\n        const date = new Date();\n        // Fixes https://github.com/emilkowalski/vaul/issues/483\n        if (element.tagName === 'SELECT') {\n            return false;\n        }\n        if (element.hasAttribute('data-vaul-no-drag') || element.closest('[data-vaul-no-drag]')) {\n            return false;\n        }\n        if (direction === 'right' || direction === 'left') {\n            return true;\n        }\n        // Allow scrolling when animating\n        if (openTime.current && date.getTime() - openTime.current.getTime() < 500) {\n            return false;\n        }\n        if (swipeAmount !== null) {\n            if (direction === 'bottom' ? swipeAmount > 0 : swipeAmount < 0) {\n                return true;\n            }\n        }\n        // Don't drag if there's highlighted text\n        if (highlightedText && highlightedText.length > 0) {\n            return false;\n        }\n        // Disallow dragging if drawer was scrolled within `scrollLockTimeout`\n        if (lastTimeDragPrevented.current && date.getTime() - lastTimeDragPrevented.current.getTime() < scrollLockTimeout && swipeAmount === 0) {\n            lastTimeDragPrevented.current = date;\n            return false;\n        }\n        if (isDraggingInDirection) {\n            lastTimeDragPrevented.current = date;\n            // We are dragging down so we should allow scrolling\n            return false;\n        }\n        // Keep climbing up the DOM tree as long as there's a parent\n        while(element){\n            // Check if the element is scrollable\n            if (element.scrollHeight > element.clientHeight) {\n                if (element.scrollTop !== 0) {\n                    lastTimeDragPrevented.current = new Date();\n                    // The element is scrollable and not scrolled to the top, so don't drag\n                    return false;\n                }\n                if (element.getAttribute('role') === 'dialog') {\n                    return true;\n                }\n            }\n            // Move up to the parent element\n            element = element.parentNode;\n        }\n        // No scrollable parents not scrolled to the top found, so drag\n        return true;\n    }\n    function onDrag(event) {\n        if (!drawerRef.current) {\n            return;\n        }\n        // We need to know how much of the drawer has been dragged in percentages so that we can transform background accordingly\n        if (isDragging) {\n            const directionMultiplier = direction === 'bottom' || direction === 'right' ? 1 : -1;\n            const draggedDistance = (pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX)) * directionMultiplier;\n            const isDraggingInDirection = draggedDistance > 0;\n            // Pre condition for disallowing dragging in the close direction.\n            const noCloseSnapPointsPreCondition = snapPoints && !dismissible && !isDraggingInDirection;\n            // Disallow dragging down to close when first snap point is the active one and dismissible prop is set to false.\n            if (noCloseSnapPointsPreCondition && activeSnapPointIndex === 0) return;\n            // We need to capture last time when drag with scroll was triggered and have a timeout between\n            const absDraggedDistance = Math.abs(draggedDistance);\n            const wrapper = document.querySelector('[data-vaul-drawer-wrapper]');\n            const drawerDimension = direction === 'bottom' || direction === 'top' ? drawerHeightRef.current : drawerWidthRef.current;\n            // Calculate the percentage dragged, where 1 is the closed position\n            let percentageDragged = absDraggedDistance / drawerDimension;\n            const snapPointPercentageDragged = getSnapPointsPercentageDragged(absDraggedDistance, isDraggingInDirection);\n            if (snapPointPercentageDragged !== null) {\n                percentageDragged = snapPointPercentageDragged;\n            }\n            // Disallow close dragging beyond the smallest snap point.\n            if (noCloseSnapPointsPreCondition && percentageDragged >= 1) {\n                return;\n            }\n            if (!isAllowedToDrag.current && !shouldDrag(event.target, isDraggingInDirection)) return;\n            drawerRef.current.classList.add(DRAG_CLASS);\n            // If shouldDrag gave true once after pressing down on the drawer, we set isAllowedToDrag to true and it will remain true until we let go, there's no reason to disable dragging mid way, ever, and that's the solution to it\n            isAllowedToDrag.current = true;\n            set(drawerRef.current, {\n                transition: 'none'\n            });\n            set(overlayRef.current, {\n                transition: 'none'\n            });\n            if (snapPoints) {\n                onDragSnapPoints({\n                    draggedDistance\n                });\n            }\n            // Run this only if snapPoints are not defined or if we are at the last snap point (highest one)\n            if (isDraggingInDirection && !snapPoints) {\n                const dampenedDraggedDistance = dampenValue(draggedDistance);\n                const translateValue = Math.min(dampenedDraggedDistance * -1, 0) * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n                return;\n            }\n            const opacityValue = 1 - percentageDragged;\n            if (shouldFade || fadeFromIndex && activeSnapPointIndex === fadeFromIndex - 1) {\n                onDragProp == null ? void 0 : onDragProp(event, percentageDragged);\n                set(overlayRef.current, {\n                    opacity: `${opacityValue}`,\n                    transition: 'none'\n                }, true);\n            }\n            if (wrapper && overlayRef.current && shouldScaleBackground) {\n                // Calculate percentageDragged as a fraction (0 to 1)\n                const scaleValue = Math.min(getScale() + percentageDragged * (1 - getScale()), 1);\n                const borderRadiusValue = 8 - percentageDragged * 8;\n                const translateValue = Math.max(0, 14 - percentageDragged * 14);\n                set(wrapper, {\n                    borderRadius: `${borderRadiusValue}px`,\n                    transform: isVertical(direction) ? `scale(${scaleValue}) translate3d(0, ${translateValue}px, 0)` : `scale(${scaleValue}) translate3d(${translateValue}px, 0, 0)`,\n                    transition: 'none'\n                }, true);\n            }\n            if (!snapPoints) {\n                const translateValue = absDraggedDistance * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Root.useEffect\": ()=>{\n            window.requestAnimationFrame({\n                \"Root.useEffect\": ()=>{\n                    shouldAnimate.current = true;\n                }\n            }[\"Root.useEffect\"]);\n        }\n    }[\"Root.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Root.useEffect\": ()=>{\n            var _window_visualViewport;\n            function onVisualViewportChange() {\n                if (!drawerRef.current || !repositionInputs) return;\n                const focusedElement = document.activeElement;\n                if (isInput(focusedElement) || keyboardIsOpen.current) {\n                    var _window_visualViewport;\n                    const visualViewportHeight = ((_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.height) || 0;\n                    const totalHeight = window.innerHeight;\n                    // This is the height of the keyboard\n                    let diffFromInitial = totalHeight - visualViewportHeight;\n                    const drawerHeight = drawerRef.current.getBoundingClientRect().height || 0;\n                    // Adjust drawer height only if it's tall enough\n                    const isTallEnough = drawerHeight > totalHeight * 0.8;\n                    if (!initialDrawerHeight.current) {\n                        initialDrawerHeight.current = drawerHeight;\n                    }\n                    const offsetFromTop = drawerRef.current.getBoundingClientRect().top;\n                    // visualViewport height may change due to somq e subtle changes to the keyboard. Checking if the height changed by 60 or more will make sure that they keyboard really changed its open state.\n                    if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {\n                        keyboardIsOpen.current = !keyboardIsOpen.current;\n                    }\n                    if (snapPoints && snapPoints.length > 0 && snapPointsOffset && activeSnapPointIndex) {\n                        const activeSnapPointHeight = snapPointsOffset[activeSnapPointIndex] || 0;\n                        diffFromInitial += activeSnapPointHeight;\n                    }\n                    previousDiffFromInitial.current = diffFromInitial;\n                    // We don't have to change the height if the input is in view, when we are here we are in the opened keyboard state so we can correctly check if the input is in view\n                    if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {\n                        const height = drawerRef.current.getBoundingClientRect().height;\n                        let newDrawerHeight = height;\n                        if (height > visualViewportHeight) {\n                            newDrawerHeight = visualViewportHeight - (isTallEnough ? offsetFromTop : WINDOW_TOP_OFFSET);\n                        }\n                        // When fixed, don't move the drawer upwards if there's space, but rather only change it's height so it's fully scrollable when the keyboard is open\n                        if (fixed) {\n                            drawerRef.current.style.height = `${height - Math.max(diffFromInitial, 0)}px`;\n                        } else {\n                            drawerRef.current.style.height = `${Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)}px`;\n                        }\n                    } else if (!isMobileFirefox()) {\n                        drawerRef.current.style.height = `${initialDrawerHeight.current}px`;\n                    }\n                    if (snapPoints && snapPoints.length > 0 && !keyboardIsOpen.current) {\n                        drawerRef.current.style.bottom = `0px`;\n                    } else {\n                        // Negative bottom value would never make sense\n                        drawerRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`;\n                    }\n                }\n            }\n            (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.addEventListener('resize', onVisualViewportChange);\n            return ({\n                \"Root.useEffect\": ()=>{\n                    var _window_visualViewport;\n                    return (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.removeEventListener('resize', onVisualViewportChange);\n                }\n            })[\"Root.useEffect\"];\n        }\n    }[\"Root.useEffect\"], [\n        activeSnapPointIndex,\n        snapPoints,\n        snapPointsOffset\n    ]);\n    function closeDrawer(fromWithin) {\n        cancelDrag();\n        onClose == null ? void 0 : onClose();\n        if (!fromWithin) {\n            setIsOpen(false);\n        }\n        setTimeout(()=>{\n            if (snapPoints) {\n                setActiveSnapPoint(snapPoints[0]);\n            }\n        }, TRANSITIONS.DURATION * 1000); // seconds to ms\n    }\n    function resetDrawer() {\n        if (!drawerRef.current) return;\n        const wrapper = document.querySelector('[data-vaul-drawer-wrapper]');\n        const currentSwipeAmount = getTranslate(drawerRef.current, direction);\n        set(drawerRef.current, {\n            transform: 'translate3d(0, 0, 0)',\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n        });\n        set(overlayRef.current, {\n            transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n            opacity: '1'\n        });\n        // Don't reset background if swiped upwards\n        if (shouldScaleBackground && currentSwipeAmount && currentSwipeAmount > 0 && isOpen) {\n            set(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: 'hidden',\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,\n                    transformOrigin: 'top'\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`,\n                    transformOrigin: 'left'\n                },\n                transitionProperty: 'transform, border-radius',\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n            }, true);\n        }\n    }\n    function cancelDrag() {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n    }\n    function onRelease(event) {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n        const swipeAmount = getTranslate(drawerRef.current, direction);\n        if (!event || !shouldDrag(event.target, false) || !swipeAmount || Number.isNaN(swipeAmount)) return;\n        if (dragStartTime.current === null) return;\n        const timeTaken = dragEndTime.current.getTime() - dragStartTime.current.getTime();\n        const distMoved = pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX);\n        const velocity = Math.abs(distMoved) / timeTaken;\n        if (velocity > 0.05) {\n            // `justReleased` is needed to prevent the drawer from focusing on an input when the drag ends, as it's not the intent most of the time.\n            setJustReleased(true);\n            setTimeout(()=>{\n                setJustReleased(false);\n            }, 200);\n        }\n        if (snapPoints) {\n            const directionMultiplier = direction === 'bottom' || direction === 'right' ? 1 : -1;\n            onReleaseSnapPoints({\n                draggedDistance: distMoved * directionMultiplier,\n                closeDrawer,\n                velocity,\n                dismissible\n            });\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        // Moved upwards, don't do anything\n        if (direction === 'bottom' || direction === 'right' ? distMoved > 0 : distMoved < 0) {\n            resetDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        if (velocity > VELOCITY_THRESHOLD) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        var _drawerRef_current_getBoundingClientRect_height;\n        const visibleDrawerHeight = Math.min((_drawerRef_current_getBoundingClientRect_height = drawerRef.current.getBoundingClientRect().height) != null ? _drawerRef_current_getBoundingClientRect_height : 0, window.innerHeight);\n        var _drawerRef_current_getBoundingClientRect_width;\n        const visibleDrawerWidth = Math.min((_drawerRef_current_getBoundingClientRect_width = drawerRef.current.getBoundingClientRect().width) != null ? _drawerRef_current_getBoundingClientRect_width : 0, window.innerWidth);\n        const isHorizontalSwipe = direction === 'left' || direction === 'right';\n        if (Math.abs(swipeAmount) >= (isHorizontalSwipe ? visibleDrawerWidth : visibleDrawerHeight) * closeThreshold) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n        resetDrawer();\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Root.useEffect\": ()=>{\n            // Trigger enter animation without using CSS animation\n            if (isOpen) {\n                set(document.documentElement, {\n                    scrollBehavior: 'auto'\n                });\n                openTime.current = new Date();\n            }\n            return ({\n                \"Root.useEffect\": ()=>{\n                    reset(document.documentElement, 'scrollBehavior');\n                }\n            })[\"Root.useEffect\"];\n        }\n    }[\"Root.useEffect\"], [\n        isOpen\n    ]);\n    function onNestedOpenChange(o) {\n        const scale = o ? (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth : 1;\n        const initialTranslate = o ? -NESTED_DISPLACEMENT : 0;\n        if (nestedOpenChangeTimer.current) {\n            window.clearTimeout(nestedOpenChangeTimer.current);\n        }\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n            transform: isVertical(direction) ? `scale(${scale}) translate3d(0, ${initialTranslate}px, 0)` : `scale(${scale}) translate3d(${initialTranslate}px, 0, 0)`\n        });\n        if (!o && drawerRef.current) {\n            nestedOpenChangeTimer.current = setTimeout(()=>{\n                const translateValue = getTranslate(drawerRef.current, direction);\n                set(drawerRef.current, {\n                    transition: 'none',\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }, 500);\n        }\n    }\n    function onNestedDrag(_event, percentageDragged) {\n        if (percentageDragged < 0) return;\n        const initialScale = (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth;\n        const newScale = initialScale + percentageDragged * (1 - initialScale);\n        const newTranslate = -NESTED_DISPLACEMENT + percentageDragged * NESTED_DISPLACEMENT;\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `scale(${newScale}) translate3d(0, ${newTranslate}px, 0)` : `scale(${newScale}) translate3d(${newTranslate}px, 0, 0)`,\n            transition: 'none'\n        });\n    }\n    function onNestedRelease(_event, o) {\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        const scale = o ? (dim - NESTED_DISPLACEMENT) / dim : 1;\n        const translate = o ? -NESTED_DISPLACEMENT : 0;\n        if (o) {\n            set(drawerRef.current, {\n                transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                transform: isVertical(direction) ? `scale(${scale}) translate3d(0, ${translate}px, 0)` : `scale(${scale}) translate3d(${translate}px, 0, 0)`\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Root.useEffect\": ()=>{\n            if (!modal) {\n                // Need to do this manually unfortunately\n                window.requestAnimationFrame({\n                    \"Root.useEffect\": ()=>{\n                        document.body.style.pointerEvents = 'auto';\n                    }\n                }[\"Root.useEffect\"]);\n            }\n        }\n    }[\"Root.useEffect\"], [\n        modal\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Root, {\n        defaultOpen: defaultOpen,\n        onOpenChange: (open)=>{\n            if (!dismissible && !open) return;\n            if (open) {\n                setHasBeenOpened(true);\n            } else {\n                closeDrawer(true);\n            }\n            setIsOpen(open);\n        },\n        open: isOpen\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DrawerContext.Provider, {\n        value: {\n            activeSnapPoint,\n            snapPoints,\n            setActiveSnapPoint,\n            drawerRef,\n            overlayRef,\n            onOpenChange,\n            onPress,\n            onRelease,\n            onDrag,\n            dismissible,\n            shouldAnimate,\n            handleOnly,\n            isOpen,\n            isDragging,\n            shouldFade,\n            closeDrawer,\n            onNestedDrag,\n            onNestedOpenChange,\n            onNestedRelease,\n            keyboardIsOpen,\n            modal,\n            snapPointsOffset,\n            activeSnapPointIndex,\n            direction,\n            shouldScaleBackground,\n            setBackgroundColorOnScale,\n            noBodyStyles,\n            container,\n            autoFocus\n        }\n    }, children));\n}\nconst Overlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ ...rest }, ref) {\n    const { overlayRef, snapPoints, onRelease, shouldFade, isOpen, modal, shouldAnimate } = useDrawerContext();\n    const composedRef = useComposedRefs(ref, overlayRef);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    // Overlay is the component that is locking scroll, removing it will unlock the scroll without having to dig into Radix's Dialog library\n    if (!modal) {\n        return null;\n    }\n    const onMouseUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Overlay.useCallback[onMouseUp]\": (event)=>onRelease(event)\n    }[\"Overlay.useCallback[onMouseUp]\"], [\n        onRelease\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Overlay, {\n        onMouseUp: onMouseUp,\n        ref: composedRef,\n        \"data-vaul-overlay\": \"\",\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? 'true' : 'false',\n        \"data-vaul-snap-points-overlay\": isOpen && shouldFade ? 'true' : 'false',\n        \"data-vaul-animate\": (shouldAnimate == null ? void 0 : shouldAnimate.current) ? 'true' : 'false',\n        ...rest\n    });\n});\nOverlay.displayName = 'Drawer.Overlay';\nconst Content = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ onPointerDownOutside, style, onOpenAutoFocus, ...rest }, ref) {\n    const { drawerRef, onPress, onRelease, onDrag, keyboardIsOpen, snapPointsOffset, activeSnapPointIndex, modal, isOpen, direction, snapPoints, container, handleOnly, shouldAnimate, autoFocus } = useDrawerContext();\n    // Needed to use transition instead of animations\n    const [delayedSnapPoints, setDelayedSnapPoints] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRef = useComposedRefs(ref, drawerRef);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const lastKnownPointerEventRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const wasBeyondThePointRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    useScaleBackground();\n    const isDeltaInDirection = (delta, direction, threshold = 0)=>{\n        if (wasBeyondThePointRef.current) return true;\n        const deltaY = Math.abs(delta.y);\n        const deltaX = Math.abs(delta.x);\n        const isDeltaX = deltaX > deltaY;\n        const dFactor = [\n            'bottom',\n            'right'\n        ].includes(direction) ? 1 : -1;\n        if (direction === 'left' || direction === 'right') {\n            const isReverseDirection = delta.x * dFactor < 0;\n            if (!isReverseDirection && deltaX >= 0 && deltaX <= threshold) {\n                return isDeltaX;\n            }\n        } else {\n            const isReverseDirection = delta.y * dFactor < 0;\n            if (!isReverseDirection && deltaY >= 0 && deltaY <= threshold) {\n                return !isDeltaX;\n            }\n        }\n        wasBeyondThePointRef.current = true;\n        return true;\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Content.useEffect\": ()=>{\n            if (hasSnapPoints) {\n                window.requestAnimationFrame({\n                    \"Content.useEffect\": ()=>{\n                        setDelayedSnapPoints(true);\n                    }\n                }[\"Content.useEffect\"]);\n            }\n        }\n    }[\"Content.useEffect\"], []);\n    function handleOnPointerUp(event) {\n        pointerStartRef.current = null;\n        wasBeyondThePointRef.current = false;\n        onRelease(event);\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Content, {\n        \"data-vaul-drawer-direction\": direction,\n        \"data-vaul-drawer\": \"\",\n        \"data-vaul-delayed-snap-points\": delayedSnapPoints ? 'true' : 'false',\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? 'true' : 'false',\n        \"data-vaul-custom-container\": container ? 'true' : 'false',\n        \"data-vaul-animate\": (shouldAnimate == null ? void 0 : shouldAnimate.current) ? 'true' : 'false',\n        ...rest,\n        ref: composedRef,\n        style: snapPointsOffset && snapPointsOffset.length > 0 ? {\n            '--snap-point-height': `${snapPointsOffset[activeSnapPointIndex != null ? activeSnapPointIndex : 0]}px`,\n            ...style\n        } : style,\n        onPointerDown: (event)=>{\n            if (handleOnly) return;\n            rest.onPointerDown == null ? void 0 : rest.onPointerDown.call(rest, event);\n            pointerStartRef.current = {\n                x: event.pageX,\n                y: event.pageY\n            };\n            onPress(event);\n        },\n        onOpenAutoFocus: (e)=>{\n            onOpenAutoFocus == null ? void 0 : onOpenAutoFocus(e);\n            if (!autoFocus) {\n                e.preventDefault();\n            }\n        },\n        onPointerDownOutside: (e)=>{\n            onPointerDownOutside == null ? void 0 : onPointerDownOutside(e);\n            if (!modal || e.defaultPrevented) {\n                e.preventDefault();\n                return;\n            }\n            if (keyboardIsOpen.current) {\n                keyboardIsOpen.current = false;\n            }\n        },\n        onFocusOutside: (e)=>{\n            if (!modal) {\n                e.preventDefault();\n                return;\n            }\n        },\n        onPointerMove: (event)=>{\n            lastKnownPointerEventRef.current = event;\n            if (handleOnly) return;\n            rest.onPointerMove == null ? void 0 : rest.onPointerMove.call(rest, event);\n            if (!pointerStartRef.current) return;\n            const yPosition = event.pageY - pointerStartRef.current.y;\n            const xPosition = event.pageX - pointerStartRef.current.x;\n            const swipeStartThreshold = event.pointerType === 'touch' ? 10 : 2;\n            const delta = {\n                x: xPosition,\n                y: yPosition\n            };\n            const isAllowedToSwipe = isDeltaInDirection(delta, direction, swipeStartThreshold);\n            if (isAllowedToSwipe) onDrag(event);\n            else if (Math.abs(xPosition) > swipeStartThreshold || Math.abs(yPosition) > swipeStartThreshold) {\n                pointerStartRef.current = null;\n            }\n        },\n        onPointerUp: (event)=>{\n            rest.onPointerUp == null ? void 0 : rest.onPointerUp.call(rest, event);\n            pointerStartRef.current = null;\n            wasBeyondThePointRef.current = false;\n            onRelease(event);\n        },\n        onPointerOut: (event)=>{\n            rest.onPointerOut == null ? void 0 : rest.onPointerOut.call(rest, event);\n            handleOnPointerUp(lastKnownPointerEventRef.current);\n        },\n        onContextMenu: (event)=>{\n            rest.onContextMenu == null ? void 0 : rest.onContextMenu.call(rest, event);\n            if (lastKnownPointerEventRef.current) {\n                handleOnPointerUp(lastKnownPointerEventRef.current);\n            }\n        }\n    });\n});\nContent.displayName = 'Drawer.Content';\nconst LONG_HANDLE_PRESS_TIMEOUT = 250;\nconst DOUBLE_TAP_TIMEOUT = 120;\nconst Handle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ preventCycle = false, children, ...rest }, ref) {\n    const { closeDrawer, isDragging, snapPoints, activeSnapPoint, setActiveSnapPoint, dismissible, handleOnly, isOpen, onPress, onDrag } = useDrawerContext();\n    const closeTimeoutIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const shouldCancelInteractionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    function handleStartCycle() {\n        // Stop if this is the second click of a double click\n        if (shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        window.setTimeout(()=>{\n            handleCycleSnapPoints();\n        }, DOUBLE_TAP_TIMEOUT);\n    }\n    function handleCycleSnapPoints() {\n        // Prevent accidental taps while resizing drawer\n        if (isDragging || preventCycle || shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        // Make sure to clear the timeout id if the user releases the handle before the cancel timeout\n        handleCancelInteraction();\n        if (!snapPoints || snapPoints.length === 0) {\n            if (!dismissible) {\n                closeDrawer();\n            }\n            return;\n        }\n        const isLastSnapPoint = activeSnapPoint === snapPoints[snapPoints.length - 1];\n        if (isLastSnapPoint && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const currentSnapIndex = snapPoints.findIndex((point)=>point === activeSnapPoint);\n        if (currentSnapIndex === -1) return; // activeSnapPoint not found in snapPoints\n        const nextSnapPoint = snapPoints[currentSnapIndex + 1];\n        setActiveSnapPoint(nextSnapPoint);\n    }\n    function handleStartInteraction() {\n        closeTimeoutIdRef.current = window.setTimeout(()=>{\n            // Cancel click interaction on a long press\n            shouldCancelInteractionRef.current = true;\n        }, LONG_HANDLE_PRESS_TIMEOUT);\n    }\n    function handleCancelInteraction() {\n        if (closeTimeoutIdRef.current) {\n            window.clearTimeout(closeTimeoutIdRef.current);\n        }\n        shouldCancelInteractionRef.current = false;\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        onClick: handleStartCycle,\n        onPointerCancel: handleCancelInteraction,\n        onPointerDown: (e)=>{\n            if (handleOnly) onPress(e);\n            handleStartInteraction();\n        },\n        onPointerMove: (e)=>{\n            if (handleOnly) onDrag(e);\n        },\n        // onPointerUp is already handled by the content component\n        ref: ref,\n        \"data-vaul-drawer-visible\": isOpen ? 'true' : 'false',\n        \"data-vaul-handle\": \"\",\n        \"aria-hidden\": \"true\",\n        ...rest\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        \"data-vaul-handle-hitarea\": \"\",\n        \"aria-hidden\": \"true\"\n    }, children));\n});\nHandle.displayName = 'Drawer.Handle';\nfunction NestedRoot({ onDrag, onOpenChange, open: nestedIsOpen, ...rest }) {\n    const { onNestedDrag, onNestedOpenChange, onNestedRelease } = useDrawerContext();\n    if (!onNestedDrag) {\n        throw new Error('Drawer.NestedRoot must be placed in another drawer');\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Root, {\n        nested: true,\n        open: nestedIsOpen,\n        onClose: ()=>{\n            onNestedOpenChange(false);\n        },\n        onDrag: (e, p)=>{\n            onNestedDrag(e, p);\n            onDrag == null ? void 0 : onDrag(e, p);\n        },\n        onOpenChange: (o)=>{\n            if (o) {\n                onNestedOpenChange(o);\n            }\n            onOpenChange == null ? void 0 : onOpenChange(o);\n        },\n        onRelease: onNestedRelease,\n        ...rest\n    });\n}\nfunction Portal(props) {\n    const context = useDrawerContext();\n    const { container = context.container, ...portalProps } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Portal, {\n        container: container,\n        ...portalProps\n    });\n}\nconst Drawer = {\n    Root,\n    NestedRoot,\n    Content,\n    Overlay,\n    Trigger: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Trigger,\n    Portal,\n    Handle,\n    Close: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Close,\n    Title: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Title,\n    Description: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Description\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vaul/dist/index.mjs\n");

/***/ })

};
;