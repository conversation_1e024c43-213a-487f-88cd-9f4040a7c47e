# Custom Database Authentication with TSYS_User Table

This project now uses custom database authentication against the `TSYS_User` table instead of Supabase Auth.

## 🔧 Database Setup

### 1. Create the TSYS_User Table

Run the SQL script in your Supabase Dashboard SQL Editor:

```sql
-- Create TSYS_User table
CREATE TABLE IF NOT EXISTS public.TSYS_User (
    id SERIAL PRIMARY KEY,
    FTUseName VARCHAR(50) UNIQUE NOT NULL,
    FTUsePwd VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Insert sample users for testing
INSERT INTO public.TSYS_User (FTUseName, FTUsePwd) VALUES 
('admin', 'admin123'),
('user1', 'password123'),
('testuser', 'test123')
ON CONFLICT (FTUseName) DO NOTHING;

-- Enable Row Level Security
ALTER TABLE public.TSYS_User ENABLE ROW LEVEL SECURITY;

-- Create policy to allow read access for authentication
CREATE POLICY "Allow select for authentication" ON public.TSYS_User
    FOR SELECT USING (true);
```

### 2. Test Credentials

You can use these test accounts:
- **Username:** `admin` | **Password:** `admin123`
- **Username:** `user1` | **Password:** `password123`
- **Username:** `testuser` | **Password:** `test123`

## 🔐 Authentication Flow

### How it Works:
1. User enters username and password on login page
2. System queries `TSYS_User` table with exact match on `FTUseName` and `FTUsePwd`
3. If match found, user is authenticated and stored in localStorage
4. Protected routes check for authenticated user
5. User can navigate between protected pages
6. Logout clears the stored user data

### Authentication Components:

#### `useCustomAuth` Hook
```tsx
import { useCustomAuth } from '../hooks/useCustomAuth'

function YourComponent() {
  const { user, loading, signIn, signOut } = useCustomAuth()
  
  // user.FTUseName - the username
  // user.id - user ID
}
```

#### Login Form
- Simple username/password form
- Validates against TSYS_User table
- Shows error messages for invalid credentials
- Redirects to intended page after successful login

## 🛡️ Security Considerations

> **⚠️ Important Security Notes:**

### Current Implementation (For Development)
- Passwords stored in plain text
- Simple exact match authentication
- Basic RLS policy

### For Production, You Should:

1. **Hash Passwords:**
```sql
-- Use bcrypt or similar for password hashing
-- Example with pgcrypto extension:
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Store hashed passwords
INSERT INTO TSYS_User (FTUseName, FTUsePwd) 
VALUES ('admin', crypt('admin123', gen_salt('bf')));

-- Verify with hashed passwords
SELECT * FROM TSYS_User 
WHERE FTUseName = 'admin' 
AND FTUsePwd = crypt('admin123', FTUsePwd);
```

2. **Implement Proper RLS:**
```sql
-- More secure RLS policies
CREATE POLICY "Users can only access own data" ON TSYS_User
    FOR ALL USING (auth.uid()::text = id::text);
```

3. **Add Additional Security:**
- Session timeouts
- Rate limiting for login attempts
- Account lockout policies
- Audit logging
- HTTPS enforcement

## 📁 File Structure

```
src/
├── hooks/
│   └── useCustomAuth.tsx        # Custom auth hook with TSYS_User
├── components/
│   ├── CustomLoginForm.tsx      # Username/password login form
│   ├── ProtectedRoute.tsx       # Updated to use custom auth
│   └── PublicRoute.tsx          # Updated to use custom auth
├── pages/
│   ├── LoginPage.tsx            # Uses CustomLoginForm
│   ├── DashboardPage.tsx        # Protected page
│   └── SettingsPage.tsx         # Shows user info
└── database/
    └── create_tsys_user_table.sql # SQL setup script
```

## 🚀 Usage

### Starting the Application
1. **Set up database:** Run the SQL script in Supabase
2. **Start dev server:** `npm run dev`
3. **Visit app:** Navigate to the URL (usually `http://localhost:5173`)
4. **Login:** Use any of the test credentials

### Adding New Users
```sql
INSERT INTO public.TSYS_User (FTUseName, FTUsePwd) 
VALUES ('newuser', 'newpassword');
```

### Checking Authentication Status
```tsx
import { useCustomAuth } from '../hooks/useCustomAuth'

function MyComponent() {
  const { user, loading } = useCustomAuth()
  
  if (loading) return <div>Loading...</div>
  if (!user) return <div>Please login</div>
  
  return <div>Welcome, {user.FTUseName}!</div>
}
```

## 🔄 Migration from Supabase Auth

The following components were updated:
- ✅ `App.jsx` - Now uses `CustomAuthProvider`
- ✅ `ProtectedRoute.tsx` - Uses `useCustomAuth`
- ✅ `PublicRoute.tsx` - Uses `useCustomAuth`
- ✅ `LoginPage.tsx` - Uses `CustomLoginForm`
- ✅ `SettingsPage.tsx` - Shows TSYS_User data
- ✅ `nav-user.tsx` - Displays username from TSYS_User

## 🧪 Testing

1. **Valid Login:**
   - Username: `admin`
   - Password: `admin123`
   - Should redirect to dashboard

2. **Invalid Login:**
   - Any wrong username/password
   - Should show error message

3. **Protected Routes:**
   - Visit `/dashboard` without login
   - Should redirect to `/login`

4. **Logout:**
   - Click logout in sidebar
   - Should return to login page

## 📝 Notes

- User session persists in localStorage
- No automatic session expiry (consider adding)
- Password validation is case-sensitive
- Username lookup is case-sensitive
