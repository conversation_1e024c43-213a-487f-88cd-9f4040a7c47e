# Use a Node.js base image
FROM node:20-alpine

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json and install dependencies
COPY package*.json ./
RUN npm install

# Copy the rest of the application code
COPY . .

# Build the React application
RUN npm run build

# Serve the built application using a simple HTTP server
# Install serve globally
RUN npm install -g serve

# Expose port 8000
EXPOSE 8000

# Command to run the application
CMD ["serve", "-s", "dist", "-l", "8000"]