# 🚨 IMMEDIATE FIX FOR 406 ERROR

## The 406 error means your TSYS_User table is not properly set up. Here's how to fix it RIGHT NOW:

### 🔧 **Step 1: Access Supabase Dashboard**
1. Go to: https://supabase.com/dashboard
2. Sign in to your account
3. Click on your project: **tbtfrakmzlekmazmxceh**

### 📝 **Step 2: Run Emergency SQL Script**
1. In your Supabase dashboard, click **"SQL Editor"** in the left sidebar
2. Click **"New Query"**
3. Copy and paste the ENTIRE content from `database/emergency_fix.sql`
4. Click **"Run"** button

### ✅ **Step 3: Verify It Worked**
After running the script, you should see results like:
```
Setup completed successfully!
total_users: 4
John <PERSON> | pass1234
admin | admin123
testuser | test123
user1 | password123
```

### 🧪 **Step 4: Test in Browser**
1. Visit your app: http://localhost:5175/
2. Open browser console (F12 → Console)
3. Click **"Test Supabase Connection"** button
4. You should see ✅ green checkmarks

### 🔍 **Step 5: Test API Directly**
Copy this into your browser console:
```javascript
fetch('https://tbtfrakmzlekmazmxceh.supabase.co/rest/v1/TSYS_User?select=*&FTUseName=eq.John Doe&FTUsePwd=eq.pass1234', {
  headers: {
    'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRidGZyYWttemxla21hem14Y2VoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNTUwMzEsImV4cCI6MjA2NzYzMTAzMX0.haTNwRyJWItpL4Yey_OqfzbAM_NA6lySsRIAEDMP-go',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRidGZyYWttemxla21hem14Y2VoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNTUwMzEsImV4cCI6MjA2NzYzMTAzMX0.haTNwRyJWItpL4Yey_OqfzbAM_NA6lySsRIAEDMP-go'
  }
}).then(r => r.json()).then(console.log)
```

Should return:
```json
[{"id":4,"FTUseName":"John Doe","FTUsePwd":"pass1234","created_at":"...","updated_at":"..."}]
```

## 🎯 **What the Emergency Script Does:**

1. ✅ **Drops existing table** (if any) to start fresh
2. ✅ **Creates TSYS_User table** with correct structure
3. ✅ **Inserts test data** including "John Doe"
4. ✅ **Disables RLS** temporarily for testing
5. ✅ **Grants full permissions** to anonymous users
6. ✅ **Verifies the setup** with test queries

## 🚀 **After Running the Script:**

- ✅ The 406 error will be gone
- ✅ Your app login will work
- ✅ API calls will return data
- ✅ Debug tools will show success

## 🔒 **Important Notes:**

> **This script disables Row Level Security for testing purposes.**
> For production, you'll want to re-enable RLS with proper policies.

But for now, this will get your authentication working immediately!

## 📞 **Still Having Issues?**

If you still get 406 errors after running the script:

1. **Check the SQL Editor output** - look for any error messages
2. **Refresh your app** - Clear browser cache
3. **Restart dev server** - `npm run dev`
4. **Try the browser console test** above

The script should fix the issue immediately!
