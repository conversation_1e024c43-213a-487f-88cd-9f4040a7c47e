# React Router DOM & Supabase Authentication Setup

This project implements protected routes using React Router DOM and Supabase authentication.

## 🚀 Features

- ✅ **Protected Routes**: Routes that require authentication
- ✅ **Public Routes**: Routes accessible only when not authenticated (like login)
- ✅ **Supabase Authentication**: Email/password and OAuth signin
- ✅ **Automatic Redirects**: Smart routing based on authentication state
- ✅ **Persistent Sessions**: User sessions persist across browser refreshes

## 📁 Project Structure

```
src/
├── components/
│   ├── ProtectedRoute.tsx      # Route wrapper for authenticated users
│   ├── PublicRoute.tsx         # Route wrapper for unauthenticated users
│   ├── AuthenticatedLayout.tsx # Layout for protected pages
│   └── LoginForm.tsx           # Login/signup form component
├── contexts/
│   └── AuthContext.tsx         # Authentication context provider
├── hooks/
│   ├── useAuth.ts              # Authentication hook
│   └── useSupabaseData.ts      # Database operations hook
├── pages/
│   ├── LoginPage.tsx           # Login page
│   ├── DashboardPage.tsx       # Main dashboard (protected)
│   └── SettingsPage.tsx        # Settings page (protected)
├── lib/
│   └── supabase.ts             # Supabase client configuration
└── App.jsx                     # Main app with routing setup
```

## 🛡️ Authentication Flow

1. **Unauthenticated users** are redirected to `/login`
2. **Authenticated users** accessing `/login` are redirected to `/dashboard`
3. **Protected routes** (`/dashboard`, `/settings`) require authentication
4. **Public routes** (`/login`) are only accessible when not authenticated

## 🔧 Route Configuration

```jsx
<Routes>
  {/* Public routes - only accessible when not authenticated */}
  <Route path="/login" element={
    <PublicRoute>
      <LoginPage />
    </PublicRoute>
  } />

  {/* Protected routes - only accessible when authenticated */}
  <Route path="/" element={
    <ProtectedRoute>
      <AuthenticatedLayout />
    </ProtectedRoute>
  }>
    <Route index element={<Navigate to="/dashboard" replace />} />
    <Route path="dashboard" element={<DashboardPage />} />
    <Route path="settings" element={<SettingsPage />} />
  </Route>

  {/* Catch all route */}
  <Route path="*" element={<Navigate to="/dashboard" replace />} />
</Routes>
```

## 🔑 Authentication Methods

- **Email/Password**: Traditional signup and signin
- **OAuth Providers**: Google, GitHub, Discord (configured in Supabase)

## 🚦 How to Use

### Adding New Protected Routes

1. Create your page component in `src/pages/`
2. Add the route to the protected routes section in `App.jsx`
3. Add navigation link to `app-sidebar.tsx` if needed

```jsx
// In App.jsx
<Route path="your-new-page" element={<YourNewPage />} />

// In app-sidebar.tsx
{
  title: "Your Page",
  url: "/your-new-page",
  icon: YourIcon,
}
```

### Adding New Public Routes

```jsx
<Route path="/your-public-route" element={
  <PublicRoute>
    <YourPublicPage />
  </PublicRoute>
} />
```

### Using Authentication in Components

```jsx
import { useAuthContext } from '../contexts/AuthContext'

function YourComponent() {
  const { user, signOut, loading } = useAuthContext()
  
  if (loading) return <div>Loading...</div>
  
  return (
    <div>
      <p>Welcome, {user?.email}!</p>
      <button onClick={signOut}>Sign Out</button>
    </div>
  )
}
```

## 🔒 Environment Variables

Make sure you have these environment variables set:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🎯 Key Components

### ProtectedRoute
Wraps routes that require authentication. Redirects to `/login` if user is not authenticated.

### PublicRoute
Wraps routes that should only be accessible to unauthenticated users. Redirects authenticated users to `/dashboard`.

### AuthContext
Provides authentication state and methods throughout the app.

## 🔄 Navigation

The sidebar navigation automatically updates based on the current route and uses React Router's `Link` component for client-side navigation.

## 🚀 Getting Started

1. **Install dependencies**: `npm install`
2. **Set up environment variables**: Copy `.env.example` to `.env` and fill in your Supabase credentials
3. **Start development server**: `npm run dev`
4. **Visit the app**: Navigate to the URL shown in terminal (usually `http://localhost:5173`)

The app will automatically redirect you to login if you're not authenticated, or to the dashboard if you are!
