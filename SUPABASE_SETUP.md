# Supabase Integration Setup

This project has been integrated with Supabase for authentication and database functionality.

## 🔧 Setup Instructions

### 1. Environment Variables
The following environment variables are already configured in `.env`:
- `VITE_SUPABASE_URL`: Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous key

### 2. Database Schema
To use the notes functionality, create a table in your Supabase database:

```sql
-- Create notes table
CREATE TABLE notes (
  id BIGSERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to see only their own notes
CREATE POLICY "Users can view their own notes" ON notes
  FOR SELECT USING (auth.uid() = user_id);

-- Create policy to allow users to insert their own notes
CREATE POLICY "Users can insert their own notes" ON notes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own notes
CREATE POLICY "Users can update their own notes" ON notes
  FOR UPDATE USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own notes
CREATE POLICY "Users can delete their own notes" ON notes
  FOR DELETE USING (auth.uid() = user_id);
```

### 3. Authentication Providers (Optional)
To enable OAuth providers like Google or GitHub:

1. Go to your Supabase dashboard
2. Navigate to Authentication → Providers
3. Enable and configure the providers you want to use
4. Add the redirect URLs for your application

## 🚀 Features Implemented

### Authentication
- ✅ Email/Password signup and signin
- ✅ OAuth providers (Google, GitHub, Discord)
- ✅ User session management
- ✅ Logout functionality
- ✅ Protected routes

### Database Integration
- ✅ Custom hook for database operations (`useSupabaseData`)
- ✅ Real-time data fetching
- ✅ CRUD operations (Create, Read, Update, Delete)
- ✅ Row Level Security support

### Components
- ✅ `LoginForm` - Authentication form
- ✅ `AuthProvider` - Context provider for auth state
- ✅ `NotesManager` - Example CRUD component
- ✅ Updated `NavUser` - Integrated with Supabase auth

## 📁 File Structure

```
src/
├── lib/
│   └── supabase.ts          # Supabase client configuration
├── hooks/
│   ├── useAuth.ts           # Authentication hook
│   └── useSupabaseData.ts   # Database operations hook
├── contexts/
│   └── AuthContext.tsx      # Authentication context
├── components/
│   ├── LoginForm.tsx        # Login/signup form
│   ├── NotesManager.tsx     # Example CRUD component
│   └── nav-user.tsx         # Updated with auth integration
└── vite-env.d.ts           # TypeScript environment definitions
```

## 🔑 Security Notes

- Environment variables are properly configured for Vite
- Row Level Security (RLS) is enabled on the database
- Authentication state is managed securely
- API keys are handled through environment variables

## 🛠 Usage Examples

### Using Authentication
```tsx
import { useAuthContext } from './contexts/AuthContext'

function MyComponent() {
  const { user, signOut } = useAuthContext()
  
  return (
    <div>
      {user ? (
        <div>
          <p>Welcome, {user.email}!</p>
          <button onClick={signOut}>Sign Out</button>
        </div>
      ) : (
        <p>Please sign in</p>
      )}
    </div>
  )
}
```

### Using Database Operations
```tsx
import { useSupabaseData } from './hooks/useSupabaseData'

function MyDataComponent() {
  const { data, loading, error, insert } = useSupabaseData('my_table')
  
  const addItem = async () => {
    await insert({ name: 'New Item' })
  }
  
  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>
  
  return (
    <div>
      {data.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
      <button onClick={addItem}>Add Item</button>
    </div>
  )
}
```

## 🎯 Next Steps

1. **Set up your database schema** using the SQL provided above
2. **Configure authentication providers** in your Supabase dashboard if needed
3. **Test the authentication flow** by running the app and signing up/in
4. **Customize the components** to match your application's needs
5. **Add more tables and relationships** as your application grows

The integration is now complete and ready to use!
