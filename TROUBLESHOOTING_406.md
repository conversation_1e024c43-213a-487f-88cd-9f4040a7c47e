# Troubleshooting 406 Not Acceptable Error

## 🚨 Issue: Getting 406 Not Acceptable when accessing TSYS_User table

The error you're experiencing is likely due to one of these issues:

### 1. 📋 **Table Not Created**
First, ensure the TSYS_User table exists in your Supabase database.

**Solution:**
1. Go to your Supabase Dashboard: https://supabase.com/dashboard
2. Select your project: `tbtfrakmzlekmazmxceh`
3. Go to **SQL Editor**
4. Run the complete SQL script from `database/create_tsys_user_table.sql`

### 2. 🔒 **Row Level Security (RLS) Issues**
RLS might be blocking API access.

**Check RLS Status:**
```sql
-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'TSYS_User';

-- Check existing policies
SELECT policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'TSYS_User';
```

**Fix RLS Issues:**
```sql
-- Ensure proper permissions
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON public.TSYS_User TO anon;
GRANT SELECT ON public.TSYS_User TO authenticated;

-- Create/recreate the policy
DROP POLICY IF EXISTS "Enable read access for all users" ON public.TSYS_User;
CREATE POLICY "Enable read access for all users" ON public.TSYS_User
    FOR SELECT USING (true);
```

### 3. 🔑 **API Key Issues**
Your anon key might not have the correct permissions.

**Verify API Key:**
- Check that your `VITE_SUPABASE_ANON_KEY` is the **anon** key, not the service role key
- The anon key should start with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

**Test API Access:**
```bash
# Test with curl (replace with your actual keys)
curl -X GET \
  'https://tbtfrakmzlekmazmxceh.supabase.co/rest/v1/TSYS_User?select=*&FTUseName=eq.admin' \
  -H 'apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json'
```

### 4. 🌐 **CORS Issues**
Make sure CORS is properly configured in Supabase.

**Check CORS Settings:**
1. Go to **Settings** → **API** in your Supabase dashboard
2. Ensure your domain is listed in allowed origins
3. For development, `http://localhost:5173` should be allowed

### 5. 🔧 **Using the Debug Tools**

I've added debug tools to your login page. When you visit the app:

1. **Open the browser console** (F12 → Console)
2. **Click "Test Supabase Connection"** button on the login page
3. **Check the results** to see what's failing

### 6. 📝 **Step-by-Step Fix**

**Step 1: Run the SQL Script**
```sql
-- Copy and paste the entire content of database/create_tsys_user_table.sql
-- into your Supabase SQL Editor and run it
```

**Step 2: Verify Table Creation**
```sql
-- Check if table exists and has data
SELECT COUNT(*) as total_users FROM public.TSYS_User;
SELECT FTUseName FROM public.TSYS_User LIMIT 5;
```

**Step 3: Test API Access**
Use the debug tools in the app or test with curl:
```bash
curl -X GET \
  'https://tbtfrakmzlekmazmxceh.supabase.co/rest/v1/TSYS_User?select=FTUseName&limit=1' \
  -H 'apikey: YOUR_ANON_KEY' \
  -H 'Authorization: Bearer YOUR_ANON_KEY'
```

**Step 4: Check App Environment**
```bash
# Restart your development server
npm run dev
```

### 7. 🆘 **If Still Not Working**

**Option A: Disable RLS Temporarily (Development Only)**
```sql
-- WARNING: Only for development/testing
ALTER TABLE public.TSYS_User DISABLE ROW LEVEL SECURITY;
```

**Option B: Use Service Role Key (Not Recommended)**
- Replace `VITE_SUPABASE_ANON_KEY` with your service role key temporarily
- **Remember to change it back** and fix RLS properly

**Option C: Check Supabase Logs**
1. Go to **Logs** → **API** in your Supabase dashboard
2. Look for recent requests and errors
3. Check what's being blocked

### 8. 🧪 **Expected Behavior After Fix**

Once fixed, you should see:
- ✅ Debug tool shows "Connection successful"
- ✅ Debug tool shows "TSYS_User table exists"
- ✅ Debug tool shows sample users
- ✅ Login with `admin/admin123` works
- ✅ No 406 errors in browser console

### 9. 📞 **Quick Test Commands**

Run these in your Supabase SQL Editor to verify everything:

```sql
-- Test 1: Table exists
SELECT table_name FROM information_schema.tables 
WHERE table_name = 'TSYS_User' AND table_schema = 'public';

-- Test 2: Data exists
SELECT FTUseName FROM public.TSYS_User;

-- Test 3: RLS status
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables WHERE tablename = 'TSYS_User';

-- Test 4: Policies exist
SELECT policyname FROM pg_policies WHERE tablename = 'TSYS_User';

-- Test 5: Permissions
SELECT grantee, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_name = 'TSYS_User';
```

If all these return expected results, the issue should be resolved!
