'use client'

import React from 'react'
import { AppSidebar } from '../../src/components/app-sidebar'
import { ChartAreaInteractive } from '../../src/components/chart-area-interactive'
import { DataTable } from '../../src/components/data-table'
import { SectionCards } from '../../src/components/section-cards'
import { SiteHeader } from '../../src/components/site-header'
import {
  SidebarInset,
  SidebarProvider,
} from '../../src/components/ui/sidebar'
import { dashboardData } from '../../src/data/dashboardData.js'
import { ClientProtectedRoute } from '../../src/components/ClientProtectedRoute'

export default function DashboardPage() {
  return (
    <ClientProtectedRoute>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar />
        <SidebarInset>
          <SiteHeader />
          <div className="flex flex-1 flex-col gap-4 p-4">
            <SectionCards />
            <div className="grid auto-rows-min gap-4 md:grid-cols-3">
              <div className="aspect-video rounded-xl bg-muted/50 md:col-span-2">
                <ChartAreaInteractive />
              </div>
              <div className="aspect-video rounded-xl bg-muted/50">
                <DataTable data={dashboardData} />
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </ClientProtectedRoute>
  )
}
