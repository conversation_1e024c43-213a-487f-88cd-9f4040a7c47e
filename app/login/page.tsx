'use client'

import React from 'react'
import { CustomLoginForm } from '../../src/components/CustomLoginForm'
import { QuickLoginTest } from '../../src/components/QuickLoginTest'
import { ClientPublicRoute } from '../../src/components/ClientPublicRoute'

export default function LoginPage() {
  return (
    <ClientPublicRoute>
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Welcome to Acme Inc.
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Please sign in to your account
            </p>
          </div>
          <CustomLoginForm />
          <QuickLoginTest />
        </div>
      </div>
    </ClientPublicRoute>
  )
}
