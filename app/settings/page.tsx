'use client'

import React from 'react'
import { AppSidebar } from '../../src/components/app-sidebar'
import { SiteHeader } from '../../src/components/site-header'
import {
  SidebarInset,
  SidebarProvider,
} from '../../src/components/ui/sidebar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../src/components/ui/card'
import { Button } from '../../src/components/ui/button'
import { useCustomAuth } from '../../src/hooks/useCustomAuth'
import { ClientProtectedRoute } from '../../src/components/ClientProtectedRoute'

export default function SettingsPage() {
  const { user, signOut } = useCustomAuth()

  const handleLogout = () => {
    signOut()
  }

  return (
    <ClientProtectedRoute>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6 px-4 lg:px-6">
              <div className="flex items-center justify-between">
                <h1 className="text-3xl font-semibold">Settings</h1>
              </div>
              
              <div className="grid gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Account Information</CardTitle>
                    <CardDescription>
                      Manage your account settings and preferences
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Username</label>
                      <p className="text-sm text-muted-foreground">{user?.FTUseName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">User ID</label>
                      <p className="text-sm text-muted-foreground">{user?.id}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Account Actions</CardTitle>
                    <CardDescription>
                      Manage your account security and access
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      variant="destructive" 
                      onClick={handleLogout}
                    >
                      Sign Out
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
    </ClientProtectedRoute>
  )
}
