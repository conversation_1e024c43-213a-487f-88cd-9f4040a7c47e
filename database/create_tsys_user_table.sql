-- SQL script to create TSYS_User table in Supabase
-- Run this in the SQL Editor of your Supabase Dashboard

-- Step 1: Create the table
CREATE TABLE IF NOT EXISTS public.TSYS_User (
    id SERIAL PRIMARY KEY,
    FTUseName VARCHAR(50) UNIQUE NOT NULL,
    FTUsePwd VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Step 2: Insert sample users for testing
INSERT INTO public.TSYS_User (FTUseName, FTUsePwd) VALUES 
('admin', 'admin123'),
('user1', 'password123'),
('testuser', 'test123'),
('<PERSON>', 'pass1234')
ON CONFLICT (FTUseName) DO NOTHING;

-- Step 3: Enable Row Level Security (RLS)
ALTER TABLE public.TSYS_User ENABLE ROW LEVEL SECURITY;

-- Step 4: Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow select for authentication" ON public.TSYS_User;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.TSYS_User;

-- Step 5: Create policy to allow read access (for login)
-- This policy allows anyone to read from the table (needed for authentication)
CREATE POLICY "Enable read access for all users" ON public.TSYS_User
    FOR SELECT USING (true);

-- Step 6: Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON public.TSYS_User TO anon;
GRANT SELECT ON public.TSYS_User TO authenticated;

-- Step 7: Verify the setup
SELECT 'Table created successfully' as status;
SELECT COUNT(*) as user_count FROM public.TSYS_User;
SELECT FTUseName FROM public.TSYS_User ORDER BY FTUseName;

-- Test the authentication query
SELECT id, FTUseName FROM public.TSYS_User 
WHERE FTUseName = 'admin' AND FTUsePwd = 'admin123';

-- Note: In production, you should:
-- 1. Hash passwords using bcrypt or similar
-- 2. Use proper RLS policies based on authenticated users
-- 3. Never store passwords in plain text
-- 4. Consider adding additional fields like email, role, etc.
-- 5. Add indexes for performance:
-- CREATE INDEX idx_tsys_user_username ON public.TSYS_User(FTUseName);
-- CREATE INDEX idx_tsys_user_credentials ON public.TSYS_User(FTUseName, FTUsePwd);
