-- EMERGENCY FIX FOR 406 ERROR
-- Copy and paste this ENTIRE script into your Supabase SQL Editor and run it

-- Step 1: Drop table if it exists (to start fresh)
DROP TABLE IF EXISTS public.TSYS_User CASCADE;

-- Step 2: Create the table
CREATE TABLE public.TSYS_User (
    id SERIAL PRIMARY KEY,
    FTUseName VARCHAR(50) UNIQUE NOT NULL,
    FTUsePwd VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Step 3: Insert test data
INSERT INTO public.TSYS_User (FTUseName, FTUsePwd) VALUES 
('admin', 'admin123'),
('user1', 'password123'),
('testuser', 'test123'),
('<PERSON>', 'pass1234');

-- Step 4: DISABLE RLS temporarily for testing
ALTER TABLE public.TSYS_User DISABLE ROW LEVEL SECURITY;

-- Step 5: Grant permissions to everyone (for testing)
GRANT ALL ON public.TSYS_User TO anon;
GRANT ALL ON public.TSYS_User TO authenticated;
GRANT ALL ON public.TSYS_User TO postgres;
GRANT USAGE, SELECT ON SEQUENCE TSYS_User_id_seq TO anon;
GRANT USAGE, SELECT ON SEQUENCE TSYS_User_id_seq TO authenticated;

-- Step 6: Verify the setup
SELECT 'Setup completed successfully!' as status;
SELECT COUNT(*) as total_users FROM public.TSYS_User;
SELECT FTUseName, FTUsePwd FROM public.TSYS_User ORDER BY FTUseName;

-- Step 7: Test the specific query that was failing
SELECT * FROM public.TSYS_User WHERE FTUseName = 'John Doe' AND FTUsePwd = 'pass1234';
