import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Define public routes that don't require authentication
const publicRoutes = ['/login']

// Define protected routes that require authentication
const protectedRoutes = ['/dashboard', '/settings']

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Check if the user has a token (we'll check localStorage on client side)
  // For now, we'll let the client-side components handle the auth logic
  // since we can't access localStorage in middleware
  
  // If it's the root path, redirect to dashboard
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
