{"name": "vite-react-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/renderer": "^4.3.0", "@supabase/supabase-js": "^2.50.3", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "file-saver": "^2.0.5", "lucide-react": "^0.525.0", "next": "^15.3.5", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-pdf-html": "^2.1.3", "react-router-dom": "^7.6.3", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "vaul": "^1.1.2", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-config-next": "^15.3.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite": "^7.0.0"}}