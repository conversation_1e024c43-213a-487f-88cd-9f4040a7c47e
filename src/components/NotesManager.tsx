import React, { useState } from 'react'
import { useSupabaseData } from '../hooks/useSupabaseData'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'

interface Note {
  id: number
  title: string
  content: string
  created_at: string
  user_id: string
}

export function NotesManager() {
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  
  const { 
    data: notes, 
    loading, 
    error, 
    insert, 
    remove 
  } = useSupabaseData<Note>('notes')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!title.trim() || !content.trim()) return

    const { error } = await insert({
      title: title.trim(),
      content: content.trim()
    })

    if (!error) {
      setTitle('')
      setContent('')
    }
  }

  const handleDelete = async (id: number) => {
    await remove(id)
  }

  if (loading) {
    return <div className="text-center p-4">Loading notes...</div>
  }

  if (error) {
    return <div className="text-center p-4 text-red-600">Error: {error}</div>
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Add New Note</CardTitle>
          <CardDescription>Create a new note using Supabase</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              placeholder="Note title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
            <textarea
              className="w-full p-3 border rounded-md resize-none"
              rows={4}
              placeholder="Note content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
            />
            <Button type="submit">Add Note</Button>
          </form>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Your Notes</h3>
        {notes.length === 0 ? (
          <p className="text-gray-500">No notes yet. Create your first note above!</p>
        ) : (
          notes.map((note) => (
            <Card key={note.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle className="text-base">{note.title}</CardTitle>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(note.id)}
                  >
                    Delete
                  </Button>
                </div>
                <CardDescription>
                  {new Date(note.created_at).toLocaleDateString()}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{note.content}</p>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
