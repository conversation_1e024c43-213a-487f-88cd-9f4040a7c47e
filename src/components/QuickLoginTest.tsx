import React, { useState } from 'react'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { useCustomAuth } from '../hooks/useCustomAuth'

export function QuickLoginTest() {
  const [testing, setTesting] = useState(false)
  const [result, setResult] = useState('')
  const { signIn } = useCustomAuth()

  const testLogin = async () => {
    setTesting(true)
    setResult('Testing login...')

    try {
      const loginResult = await signIn('John Doe', 'pass1234')
      
      if (loginResult.error) {
        setResult(`❌ Login failed: ${loginResult.error}`)
      } else if (loginResult.user) {
        setResult(`✅ Login successful! Welcome ${loginResult.user.FTUseName}`)
      }
    } catch (error) {
      setResult(`❌ Error: ${error}`)
    }

    setTesting(false)
  }

  return (
    <Card className="w-full max-w-md mx-auto mt-4">
      <CardHeader>
        <CardTitle className="text-sm">Quick Login Test</CardTitle>
      </CardHeader>
      <CardContent>
        <Button 
          onClick={testLogin} 
          disabled={testing}
          size="sm"
          variant="outline"
          className="w-full mb-4"
        >
          {testing ? 'Testing...' : 'Test Login (John Doe / pass1234)'}
        </Button>
        
        {result && (
          <div className="bg-gray-50 p-3 rounded text-xs">
            {result}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
