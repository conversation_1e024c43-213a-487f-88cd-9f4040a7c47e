import React, { useState } from 'react'
import { But<PERSON> } from './ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card'
import { testSupabaseConnection, testLogin } from '../utils/supabaseTest'

export function SupabaseDebugger() {
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<string[]>([])

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const runTests = async () => {
    setTesting(true)
    setResults([])
    
    addResult('🔍 Starting Supabase connection tests...')
    
    try {
      // Test direct API call first
      addResult('📡 Testing direct API access...')
      const response = await fetch(`https://tbtfrakmzlekmazmxceh.supabase.co/rest/v1/TSYS_User?select=FTUseName&limit=1`, {
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRidGZyYWttemxla21hem14Y2VoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNTUwMzEsImV4cCI6MjA2NzYzMTAzMX0.haTNwRyJWItpL4Yey_OqfzbAM_NA6lySsRIAEDMP-go',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRidGZyYWttemxla21hem14Y2VoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNTUwMzEsImV4cCI6MjA2NzYzMTAzMX0.haTNwRyJWItpL4Yey_OqfzbAM_NA6lySsRIAEDMP-go',
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        addResult(`❌ Direct API call failed: ${response.status} ${response.statusText}`)
        addResult('💡 This means the TSYS_User table needs to be created or RLS needs to be configured')
        addResult('📝 Please run the SQL script: database/emergency_fix.sql')
        setTesting(false)
        return
      }
      
      const directData = await response.json()
      addResult(`✅ Direct API call successful: ${directData.length} records found`)
      
    } catch (error) {
      addResult(`❌ Direct API test failed: ${error}`)
      setTesting(false)
      return
    }
    
    // Test connection
    const connectionOk = await testSupabaseConnection()
    
    if (connectionOk) {
      addResult('✅ Supabase client tests passed')
      
      // Test login with sample credentials
      addResult('🔐 Testing login with sample credentials...')
      
      const loginTest = await testLogin('admin', 'admin123')
      if (loginTest.success) {
        addResult('✅ Login test successful')
      } else {
        addResult(`❌ Login test failed: ${loginTest.error}`)
      }
      
      // Test John Doe specifically
      const johnDoeTest = await testLogin('John Doe', 'pass1234')
      if (johnDoeTest.success) {
        addResult('✅ John Doe login test successful')
      } else {
        addResult(`❌ John Doe login test failed: ${johnDoeTest.error}`)
      }
    } else {
      addResult('❌ Supabase client tests failed')
    }
    
    setTesting(false)
  }

  return (
    <Card className="w-full max-w-md mx-auto mt-4">
      <CardHeader>
        <CardTitle className="text-sm">Debug Tools</CardTitle>
      </CardHeader>
      <CardContent>
        <Button 
          onClick={runTests} 
          disabled={testing}
          size="sm"
          variant="outline"
          className="w-full mb-4"
        >
          {testing ? 'Testing...' : 'Test Supabase Connection'}
        </Button>
        
        {results.length > 0 && (
          <div className="bg-gray-50 p-3 rounded text-xs font-mono max-h-40 overflow-y-auto">
            {results.map((result, index) => (
              <div key={index} className="mb-1">
                {result}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
