import React, { useState } from 'react'
import { Notes<PERSON>anager } from './NotesManager'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'

export function SupabaseDemo() {
  const [showNotes, setShowNotes] = useState(false)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Supabase Integration Demo</CardTitle>
          <CardDescription>
            This demonstrates the Supabase integration with authentication and database operations.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-gray-600">
              ✅ Authentication is working (you're signed in!)
            </p>
            <p className="text-sm text-gray-600">
              ✅ Environment variables are configured
            </p>
            <p className="text-sm text-gray-600">
              ✅ Supabase client is connected
            </p>
          </div>
          
          <Button 
            onClick={() => setShowNotes(!showNotes)}
            variant={showNotes ? "outline" : "default"}
          >
            {showNotes ? "Hide" : "Show"} Database Demo
          </Button>
          
          {showNotes && (
            <div className="mt-4 p-4 border rounded-lg bg-gray-50">
              <p className="text-sm text-gray-600 mb-4">
                <strong>Note:</strong> To use the notes feature, you need to create the 'notes' table in your Supabase database. 
                See SUPABASE_SETUP.md for the SQL schema.
              </p>
              <NotesManager />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
