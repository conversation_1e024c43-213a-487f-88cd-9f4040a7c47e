export const dashboardData = [
  {
    "id": 1,
    "header": "Cover page",
    "type": "Cover page",
    "status": "In Process",
    "target": "18",
    "limit": "5",
    "reviewer": "Eddie Lake"
  },
  {
    "id": 2,
    "header": "Table of contents",
    "type": "Table of contents",
    "status": "Done",
    "target": "29",
    "limit": "24",
    "reviewer": "Eddie Lake"
  },
  {
    "id": 3,
    "header": "Executive summary",
    "type": "Narrative",
    "status": "Done",
    "target": "10",
    "limit": "13",
    "reviewer": "Eddie Lake"
  },
  {
    "id": 4,
    "header": "Technical approach",
    "type": "Narrative",
    "status": "Done",
    "target": "27",
    "limit": "23",
    "reviewer": "<PERSON><PERSON>"
  },
  {
    "id": 5,
    "header": "Design",
    "type": "Narrative",
    "status": "In Process",
    "target": "25",
    "limit": "8",
    "reviewer": "<PERSON><PERSON>"
  },
  {
    "id": 6,
    "header": "Management",
    "type": "Narrative",
    "status": "Done",
    "target": "28",
    "limit": "11",
    "reviewer": "<PERSON><PERSON>"
  },
  {
    "id": 7,
    "header": "Schedule",
    "type": "Narrative",
    "status": "Done",
    "target": "21",
    "limit": "7",
    "reviewer": "Jamik Tashpulatov"
  },
  {
    "id": 8,
    "header": "Price",
    "type": "Narrative",
    "status": "Done",
    "target": "16",
    "limit": "9",
    "reviewer": "Jamik Tashpulatov"
  },
  {
    "id": 9,
    "header": "Risk Management Strategies",
    "type": "Narrative",
    "status": "Done",
    "target": "7",
    "limit": "10",
    "reviewer": "Assign reviewer"
  },
  {
    "id": 10,
    "header": "Quality Assurance Methodology",
    "type": "Narrative",
    "status": "Done",
    "target": "5",
    "limit": "30",
    "reviewer": "Assign reviewer"
  }
]
