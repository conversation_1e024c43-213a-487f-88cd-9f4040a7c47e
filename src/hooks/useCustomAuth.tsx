import { useState, useEffect, createContext, useContext } from 'react'
import { supabase } from '../lib/supabase'

interface User {
  id: string
  FTUseName: string
  // Add other fields from your TSYS_User table as needed
}

interface CustomAuthContextType {
  user: User | null
  loading: boolean
  signIn: (username: string, password: string) => Promise<{ user?: User; error?: string }>
  signOut: () => void
}

const CustomAuthContext = createContext<CustomAuthContextType | undefined>(undefined)

export function CustomAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is stored in localStorage on app start
    const storedUser = localStorage.getItem('tsys_user')
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser))
      } catch (error) {
        localStorage.removeItem('tsys_user')
      }
    }
    setLoading(false)
  }, [])

  const signIn = async (username: string, password: string) => {
    setLoading(true)
    try {
      console.log('🔐 Attempting login for user:', username)
      
      // Query the TSYS_User table with proper Supabase client
      const { data, error } = await supabase
        .from('TSYS_User')
        .select('*')
        .eq('FTUseName', username)
        .eq('FTUsePwd', password)
        .maybeSingle() // Use maybeSingle() instead of single() to avoid errors when no match

      if (error) {
        console.error('❌ Supabase error:', error)
        setLoading(false)
        return { error: `Database error: ${error.message}` }
      }

      if (!data) {
        console.log('❌ No matching user found for:', username)
        setLoading(false)
        return { error: 'Invalid username or password' }
      }

      console.log('✅ Login successful for user:', data.FTUseName)
      
      const userData = {
        id: data.id || data.FTUseName, // Use id if available, otherwise username
        FTUseName: data.FTUseName,
        // Add other fields you need from the TSYS_User table
      }

      setUser(userData)
      localStorage.setItem('tsys_user', JSON.stringify(userData))
      setLoading(false)
      return { user: userData }
    } catch (err) {
      console.error('❌ Login error:', err)
      setLoading(false)
      return { error: 'Login failed. Please check your connection and try again.' }
    }
  }

  const signOut = () => {
    setUser(null)
    localStorage.removeItem('tsys_user')
  }

  return (
    <CustomAuthContext.Provider value={{ user, loading, signIn, signOut }}>
      {children}
    </CustomAuthContext.Provider>
  )
}

export function useCustomAuth() {
  const context = useContext(CustomAuthContext)
  if (context === undefined) {
    throw new Error('useCustomAuth must be used within a CustomAuthProvider')
  }
  return context
}
