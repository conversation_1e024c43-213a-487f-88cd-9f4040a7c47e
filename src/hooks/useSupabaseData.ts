import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

export function useSupabaseData<T>(
  table: string,
  select: string = '*',
  filters?: Record<string, any>
) {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchData()
  }, [table, select, filters])

  const fetchData = async () => {
    try {
      setLoading(true)
      let query = supabase.from(table).select(select)
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          query = query.eq(key, value)
        })
      }

      const { data: result, error } = await query

      if (error) {
        setError(error.message)
      } else {
        setData((result as T[]) || [])
        setError(null)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const insert = async (values: Partial<T>) => {
    const { data, error } = await supabase
      .from(table)
      .insert([values])
      .select()

    if (!error) {
      await fetchData() // Refresh data
    }

    return { data, error }
  }

  const update = async (id: string | number, values: Partial<T>) => {
    const { data, error } = await supabase
      .from(table)
      .update(values)
      .eq('id', id)
      .select()

    if (!error) {
      await fetchData() // Refresh data
    }

    return { data, error }
  }

  const remove = async (id: string | number) => {
    const { data, error } = await supabase
      .from(table)
      .delete()
      .eq('id', id)

    if (!error) {
      await fetchData() // Refresh data
    }

    return { data, error }
  }

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    insert,
    update,
    remove,
  }
}
