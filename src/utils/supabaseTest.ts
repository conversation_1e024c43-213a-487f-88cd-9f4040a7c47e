import { supabase } from '../lib/supabase'

// Debug function to test Supabase connection
export async function testSupabaseConnection() {
  try {
    console.log('Testing Supabase connection...')
    
    // Test 1: Direct API call to verify the table exists and API works
    console.log('📡 Testing direct API access...')
    try {
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/rest/v1/TSYS_User?select=count&limit=1`,
        {
          headers: {
            'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
            'Prefer': 'count=exact'
          }
        }
      )
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ Direct API call successful:', data.length, 'records found')
      } else {
        console.log('❌ Direct API call failed:', response.status, response.statusText)
        return false
      }
    } catch (apiError) {
      console.error('❌ Direct API call error:', apiError)
      return false
    }
    
    // Test 2: Test Supabase client with TSYS_User table directly
    console.log('🔧 Testing Supabase client...')
    try {
      const { data: userTable, error: userTableError } = await supabase
        .from('TSYS_User')
        .select('count(*)', { count: 'exact' })
        .limit(1)
      
      if (userTableError) {
        console.error('❌ TSYS_User table not accessible via client:', userTableError)
        return false
      }
      
      console.log('✅ TSYS_User table accessible via Supabase client')
    } catch (clientError) {
      console.error('❌ Supabase client error:', clientError)
      return false
    }
    
    // Test 3: Try to fetch sample data
    console.log('📊 Testing data retrieval...')
    const { data: sampleData, error: sampleError } = await supabase
      .from('TSYS_User')
      .select('FTUseName')
      .limit(3)
    
    if (sampleError) {
      console.error('❌ Error fetching sample data:', sampleError)
      return false
    }
    
    console.log('✅ Sample users found:', sampleData?.map(u => u.FTUseName))
    
    return true
  } catch (error) {
    console.error('❌ Connection test failed:', error)
    return false
  }
}

// Function to test login with specific credentials
export async function testLogin(username: string, password: string) {
  try {
    console.log(`🔐 Testing login for username: ${username}`)
    
    const { data, error } = await supabase
      .from('TSYS_User')
      .select('*')
      .eq('FTUseName', username)
      .eq('FTUsePwd', password)
      .maybeSingle()
    
    if (error) {
      console.error('❌ Login test error:', error)
      return { success: false, error: error.message }
    }
    
    if (!data) {
      console.log('❌ No matching user found')
      return { success: false, error: 'Invalid credentials' }
    }
    
    console.log('✅ Login test successful for user:', data.FTUseName)
    return { success: true, user: data }
  } catch (error) {
    console.error('❌ Login test failed:', error)
    return { success: false, error: 'Connection failed' }
  }
}
