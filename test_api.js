// Browser Console Test Script
// Copy and paste this into your browser console after running the SQL script

const SUPABASE_URL = 'https://tbtfrakmzlekmazmxceh.supabase.co'
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRidGZyYWttemxla21hem14Y2VoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNTUwMzEsImV4cCI6MjA2NzYzMTAzMX0.haTNwRyJWItpL4Yey_OqfzbAM_NA6lySsRIAEDMP-go'

async function testTSYSUser() {
  console.log('🧪 Testing TSYS_User table access...')
  
  try {
    // Test 1: Get all users
    const response1 = await fetch(`${SUPABASE_URL}/rest/v1/TSYS_User?select=*`, {
      headers: {
        'apikey': SUPABASE_KEY,
        'Authorization': `Bearer ${SUPABASE_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (!response1.ok) {
      console.error('❌ Test 1 failed:', response1.status, response1.statusText)
      return
    }
    
    const users = await response1.json()
    console.log('✅ Test 1 passed - All users:', users)
    
    // Test 2: Test John <PERSON>e specifically
    const response2 = await fetch(`${SUPABASE_URL}/rest/v1/TSYS_User?select=*&FTUseName=eq.John Doe&FTUsePwd=eq.pass1234`, {
      headers: {
        'apikey': SUPABASE_KEY,
        'Authorization': `Bearer ${SUPABASE_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (!response2.ok) {
      console.error('❌ Test 2 failed:', response2.status, response2.statusText)
      return
    }
    
    const johnDoe = await response2.json()
    console.log('✅ Test 2 passed - John Doe login:', johnDoe)
    
    // Test 3: Test admin login
    const response3 = await fetch(`${SUPABASE_URL}/rest/v1/TSYS_User?select=*&FTUseName=eq.admin&FTUsePwd=eq.admin123`, {
      headers: {
        'apikey': SUPABASE_KEY,
        'Authorization': `Bearer ${SUPABASE_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (!response3.ok) {
      console.error('❌ Test 3 failed:', response3.status, response3.statusText)
      return
    }
    
    const admin = await response3.json()
    console.log('✅ Test 3 passed - Admin login:', admin)
    
    console.log('🎉 All tests passed! Your TSYS_User table is working correctly.')
    
  } catch (error) {
    console.error('❌ Test failed with error:', error)
  }
}

// Run the test
testTSYSUser()
